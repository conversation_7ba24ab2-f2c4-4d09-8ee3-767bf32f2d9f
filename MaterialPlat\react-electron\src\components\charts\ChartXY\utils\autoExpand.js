import { INTERVAL_TYPE, PROPORTION_TYPE } from '../constants/axis'

/**
 * 处理正向位置扩展
*/
const handelExtend = (axis, interval, lineInterval) => {
    const { start, end } = axis.getInterval()

    // 如果没有数据，则返回原数据
    if (lineInterval.max === 0 && lineInterval.min === 0) {
        return interval
    }

    // 如果配置的interval和轴的interval 大于 lineInterval.max
    if ((interval.end > lineInterval.max) || end > lineInterval.max) {
        return { start, end }
    }

    // 扩展
    return {
        start,
        end: start + (lineInterval.max - start) * 1.1
    }
}

/**
 * 处理负向位置扩展
*/
const handelNegativeExtend = (axis, interval, lineInterval) => {
    const { start, end } = axis.getInterval()
    const tempInterval = {
        start,
        end
    }
    // 如果没有数据，则返回原数据
    if (lineInterval.max === 0 && lineInterval.min === 0) {
        return interval
    }
    // 如果配置的interval和轴的interval 小于 lineInterval.min
    if ((interval.start < lineInterval.min)
        || start < lineInterval.min) {
        return { start, end }
    }
    // 扩展
    return { ...tempInterval, start: end + (lineInterval.min - end) * 1.1 }
}

/**
 * 处理应用范围
*/
const handelApplyExtend = (axis, interval, lineInterval) => {
    const tempInterval = {
        ...interval
    }
    const extend = handelExtend(axis, interval, lineInterval)
    const negativeExtend = handelNegativeExtend(axis, interval, lineInterval)
    tempInterval.start = negativeExtend.start
    tempInterval.end = extend.end
    return tempInterval
}

const handleProportion = ({
    type,
    interval,
    lineInterval,
    axis
}) => {
    if (!lineInterval) {
        return
    }

    switch (type) {
    case PROPORTION_TYPE.正向位置扩展:
        axis.setInterval(handelExtend(axis, interval, lineInterval))
        break
    case PROPORTION_TYPE.负向位置扩展:
        axis.setInterval(handelNegativeExtend(axis, interval, lineInterval))
        break
    case PROPORTION_TYPE.应用范围:
        axis.setInterval(handelApplyExtend(axis, interval, lineInterval))
        break
    case PROPORTION_TYPE.数据范围:
        axis.setInterval({ start: lineInterval.min, end: lineInterval.max })
        break
    case PROPORTION_TYPE.扫描范围:
        axis.setInterval({ start: lineInterval.last - interval?.lastRange, end: lineInterval.last })
        break
    default:
        break
    }
}

const getLinesRange = (lines) => {
    if (!lines || lines.length === 0) {
        return null
    }
    const lineRange = lines.map(line => {
        if (line.getPointAmount() === 0) {
            return null
        }

        return ({
            XMax: line.getXMax(),
            XMin: line.getXMin(),
            YMax: line.getYMax(),
            YMin: line.getYMin(),
            lastX: line.getLastPoint()?.x ?? 0,
            lastY: line.getLastPoint()?.y ?? 0
        })
    })

    if (lineRange.every(i => i === null)) {
        return null
    }

    return {
        XMax: Math.max(...lineRange.filter(i => i !== null).map(i => i.XMax)),
        XMin: Math.min(...lineRange.filter(i => i !== null).map(i => i.XMin)),
        YMax: Math.max(...lineRange.filter(i => i !== null).map(i => i.YMax)),
        YMin: Math.min(...lineRange.filter(i => i !== null).map(i => i.YMin)),
        lastX: Math.max(...lineRange.filter(i => i !== null).map(i => i.lastX)),
        lastY: Math.min(...lineRange.filter(i => i !== null).map(i => i.lastY))
    }
}

const axisAutoExpand = ({
    isX, option, axis, lines
}) => {
    const {
        proportionType,
        start,
        end,
        isLog,
        intervalNumber,
        lastRange
    } = option.interval

    if (isLog) {
        return
    }

    // 获取轴上所有的线
    // 计算最大最小值
    const range = getLinesRange(lines)

    // 应用范围
    handleProportion({
        type: proportionType,
        interval: {
            start,
            end,
            lastRange
        },
        lineInterval: range && {
            min: isX ? range.XMin : range.YMin,
            max: isX ? range.XMax : range.YMax,
            last: isX ? range.lastX : range.lastY
        },
        axis
    })
}

export const handleAutoExpand = ({
    option, xAxisMapRef, yAxisMapRef, lineMapRef
}) => {
    try {
        const { xAxis, yAxis, lines } = option

        xAxis.forEach(x => {
            axisAutoExpand({
                isX: true,
                option: x,
                axis: xAxisMapRef.current[x.id],
                lines: lines.filter(l => l.xAxisId === x.id).map(l => lineMapRef.current[l.id])
            })
        })

        yAxis.forEach(y => {
            axisAutoExpand({
                isX: false,
                option: y,
                axis: yAxisMapRef.current[y.id],
                lines: lines.filter(l => l.yAxisId === y.id).map(l => lineMapRef.current[l.id])
            })
        })
    } catch (error) {
        console.log('err', error)
    }
}
