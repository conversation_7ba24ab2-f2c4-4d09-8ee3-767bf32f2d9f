using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MQ;
using FuncLibs;
using DocumentFormat.OpenXml.Office.CustomUI;

namespace ScriptEngine.InputVar.UISubscription
{
    
    /// <summary>
    /// UI订阅管理器
    /// 负责管理所有Buffer相关的UI订阅，包括订阅的创建、更新、暂停、恢复和删除
    /// </summary>
    public class UISubscriptionManager : IDisposable
    {
        private readonly ConcurrentDictionary<string, Subscription> _subscriptions= new();
        private readonly ConcurrentDictionary<string, SubscriptionRequest> subscriptionRequests = new();
        private bool _disposed = false;
        public UISubscriptionManager()
        {

        }

        /// <summary>
        /// 初始化订阅
        /// </summary>
        /// <param name="request">订阅请求</param>
        /// <returns>订阅ID</returns>
        public bool InitializeSubscription(SubscriptionRequest request)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request));
            }

            Subscription? subscription = null;
            try
            {
                // 如果订阅ID已存在，先移除旧订阅
                if (_subscriptions.ContainsKey(request.ControlCompId))
                {
                    CloseSubscription(request.ControlCompId);
                }
                // 更新订阅
                switch (request.DataSourceType)
                {
                    case "daqbuffer":
                        // 创建新订阅
                        subscription = new BufferSubscription(request);
                        break;
                    case "doubleArray":
                        subscription = new DoubleArraySubscription(request);
                        break;
                    case "doubleArraySet":
                        subscription = new DoubleArrayListSubscription(request);
                        break;
                    default:
                        throw new InvalidOperationException($"未知订阅类型DataSourceType:{request.DataSourceType} ControlCompId： {request.ControlCompId}");
                }
                // 添加到管理器
                if (_subscriptions.TryAdd(request.ControlCompId, subscription))
                {
                    subscriptionRequests.TryRemove(request.ControlCompId, out _);
                    subscriptionRequests.TryAdd(request.ControlCompId, request);
                    // 启动订阅
                    subscription.Start();
                    return true;
                }
                else
                {
                    throw new InvalidOperationException($"订阅添加失败: {request.ControlCompId}");
                }
            }
            catch (Exception ex)
            {
                if (subscription != null)
                {
                    subscription.Dispose();
                }
                Logger.Error($"初始化订阅失败: {request.ControlCompId}, 错误: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 更新订阅
        /// </summary>
        /// <param name="subscriptionId">订阅ID</param>
        /// <param name="request">新的订阅请求</param>
        /// <returns>是否更新成功</returns>
        public bool UpdateSubscription(SubscriptionRequest request)
        {
            try
            {
                //重新订阅
                return InitializeSubscription(request);
            }
            catch (Exception ex)
            {
                Logger.Error($"更新订阅失败: {request.ControlCompId}, 错误: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 暂停订阅
        /// </summary>
        /// <param name="subscriptionId">订阅ID</param>
        /// <returns>是否暂停成功</returns>
        public bool PauseSubscription(string subscriptionId)
        {
            if (string.IsNullOrEmpty(subscriptionId))
            {
                return false;
            }

            try
            {
                if (_subscriptions.TryGetValue(subscriptionId, out var subscription))
                {
                    subscription.Pause();
                    return true;
                }
                else
                {
                    Logger.Error($"订阅不存在，无法暂停: {subscriptionId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"暂停订阅失败: {subscriptionId}, 错误: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 恢复订阅
        /// </summary>
        /// <param name="subscriptionId">订阅ID</param>
        /// <returns>是否恢复成功</returns>
        public bool ResumeSubscription(string subscriptionId)
        {
            if (string.IsNullOrEmpty(subscriptionId))
            {
                return false;
            }

            try
            {
                if (_subscriptions.TryGetValue(subscriptionId, out var subscription))
                {
                    subscription.Resume();
                    return true;
                }
                else
                {
                    Logger.Error($"订阅不存在，无法恢复: {subscriptionId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"恢复订阅失败: {subscriptionId}, 错误: {ex}");
                throw;
            }
        }
        /// <summary>
        /// 重新启动本项目订阅
        /// </summary>
        public void RestartTemplateSubscription(string templateName)
        {
            Logger.Info("重新启动所有订阅");
            foreach (var request in _subscriptions)
            {
                if (subscriptionRequests.TryGetValue(request.Key, out var subscriptionRequest))
                {
                    if (subscriptionRequest.TemplateName == templateName)
                    {
                        InitializeSubscription(subscriptionRequest);
                    }
                }
            }
        }
        /// <summary>
        /// 关闭订阅
        /// </summary>
        /// <param name="subscriptionId">订阅ID</param>
        /// <returns>是否关闭成功</returns>
        public bool CloseSubscription(string subscriptionId)
        {
            if (string.IsNullOrEmpty(subscriptionId))
            {
                return false;
            }

            try
            {
                if (_subscriptions.TryRemove(subscriptionId, out var subscription))
                {
                    subscriptionRequests.TryRemove(subscriptionId, out _);
                    subscription.Stop();
                    subscription.Dispose();
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"关闭订阅失败: {subscriptionId}, 错误: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 关闭模板的所有订阅
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <returns>成功关闭的订阅数量</returns>
        public bool CloseTemplateSubscriptions(string templateName)
        {
            
            var bufferSubscriptions = _subscriptions.Where(x=>x.Value.Request.TemplateName==templateName).Select(x=>x.Value).ToList();
            foreach (var item in bufferSubscriptions)
            {
                item.Stop();
            }
            return true;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            try
            {
                
                // 关闭所有订阅
                var subscriptionIds = _subscriptions.Keys.ToList();
                var closeTasks = subscriptionIds.Select(CloseSubscription);
               
            }
            catch (Exception ex)
            {
                Logger.Error($"释放 BufferUISubscriptionManager 时发生错误: {ex}");
            }
        }
    }

    /// <summary>
    /// 订阅信息类
    /// </summary>
    public class SubscriptionInfo
    {
        public string SubscriptionId { get; set; }
        public SubscriptionStatus Status { get; set; }
        public SubscriptionRequest Request { get; set; }
    }
}