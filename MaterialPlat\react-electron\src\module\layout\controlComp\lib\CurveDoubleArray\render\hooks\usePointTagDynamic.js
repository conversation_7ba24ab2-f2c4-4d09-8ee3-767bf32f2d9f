import { useEffect, useMemo, useRef } from 'react'
import { useSelector } from 'react-redux'
import { cloneDeep, isEqual } from 'lodash'

import { config2ChartOption } from '../utils/config2ChartOption'

const usePointTagDynamic = ({
    chartOption, config, chartXYRef, compStatus, updateConfig
}) => {
    const currentConfig = useRef()

    const auxiliaryLineList = useSelector(state => state.template.auxiliaryLineList)
    const optSample = useSelector(state => state.project.optSample)
    const resultHistoryData = useSelector(state => state.project.resultHistoryData)
    const resultData = useSelector(state => state.template.resultData)

    const pointTagPositionRef = useRef()

    useEffect(() => {
        currentConfig.current = chartOption.markerPoint.map((i) => ([i.id]))
    }, [chartOption])

    useEffect(() => {
        const { markerPoint } = config2ChartOption(config, compStatus)
        if (!isEqual(markerPoint, currentConfig.current)) {
            markerPoint.forEach((i) => {
                const prev = currentConfig.current?.find((j) => j.id === i.id)
                if (!prev || prev.pointIndex !== i.pointIndex || prev.title !== i.title || prev.position !== i.position) {
                    chartXYRef.current.updateAnnotationPosition(i.id, i.pointIndex, i.title, i.position)
                }
            })

            currentConfig.current = markerPoint
        }
    }, [auxiliaryLineList, optSample, resultHistoryData, resultData])

    useEffect(() => {
        if (!isEqual(compStatus?.pointTag?.position, pointTagPositionRef.current)) {
            const { markerPoint } = config2ChartOption(config, compStatus)

            markerPoint.forEach((i) => {
                const prev = currentConfig.current?.find((j) => j.id === i.id)
                if (!prev || prev.pointIndex !== i.pointIndex || prev.title !== i.title || prev.position !== i.position) {
                    chartXYRef.current.updateAnnotationPosition(i.id, i.pointIndex, i.title, i.position)
                }
            })

            currentConfig.current = markerPoint
        }
    }, [compStatus])

    // 处理点标签位置持久化
    const updatePointTagPosition = (c) => {
        if (config) {
            const newConfig = {
                ...config,
                compStatus: {
                    ...(compStatus ?? {}),
                    pointTag: {
                        ...(compStatus?.pointTag ?? {}),
                        position: {
                            ...(compStatus?.pointTag?.position ?? {}),
                            // 保存
                            [c.id]: c.position
                        }
                    }
                }
            }

            pointTagPositionRef.current = cloneDeep(newConfig.compStatus.pointTag.position)

            updateConfig(newConfig)
        }
    }

    return {
        updatePointTagPosition
    }
}

export default usePointTagDynamic
