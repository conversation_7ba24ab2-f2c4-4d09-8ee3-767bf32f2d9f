import { useMemo, useRef, useEffect } from 'react'
import { useSelector } from 'react-redux'

import useSubScriberCompMsg from '@/hooks/subscribe/useSubScriberCompMsg'

import { SOURCE_TYPE } from '../../constants/constants'

// 处理曲线数据的通用函数
const processLineData = (chartXYRef, chartOption, mode, data, dataSourceKey) => {
    const isUpdate = mode === 0
    const shouldChangeLines = chartOption.lines.filter(i => i.dataSourceKey === dataSourceKey)

    shouldChangeLines.forEach(line => {
        if (isUpdate) {
            chartXYRef.current?.clearLine(line.id)
        }

        const points = []

        for (let i = 0; i < data[line?.xSignal]?.length ?? 0; i += 1) {
            points.push({
                x: data?.[line?.xSignal]?.[i],
                y: data?.[line?.ySignal]?.[i]
            })
        }

        chartXYRef.current.lineAdd(line.id, points)
    })
}

const getDataSourceKey = ({
    isBufferCurve, sourceType, sampleCode, doubleArrayIndex
}) => {
    if (isBufferCurve) {
        if (sourceType === SOURCE_TYPE.多数据源) {
            return sampleCode
        }

        return 'optSample'
    }

    return doubleArrayIndex.toString()
}

const useData = ({
    isBufferCurve,
    id, config, chartOption, chartXYRef, isLocked
}) => {
    const optSample = useSelector(state => state.project.optSample)

    const highlightLineId = useMemo(() => {
        if (isBufferCurve && optSample && config?.base?.sourceType === SOURCE_TYPE.多数据源) {
            return config.curveGroup.yAxis?.curves?.[optSample?.code]?.lines?.[0]?.id
        }

        return null
    }, [isBufferCurve, optSample, config])

    // 锁定状态下的数据缓存
    const lockedDataRef = useRef({
        // { [doubleArrayIndex]: [{ mode, data }, { mode, data }, ...] }
    })

    useSubScriberCompMsg({
        controlCompId: id,
        onMessage: (msg) => {
            const {
                mode, data, doubleArrayIndex, sampleCode
            } = msg

            const dataSourceKey = getDataSourceKey({
                isBufferCurve,
                sourceType: config?.base?.sourceType,
                sampleCode,
                doubleArrayIndex
            })

            // 如果当前处于锁定状态，将数据保存到缓存中
            if (isLocked) {
                // 如果mode为0（更新模式），清空对应doubleArrayIndex的锁定保存数据
                if (mode === 0) {
                    lockedDataRef.current[dataSourceKey] = []
                }

                // 初始化数组（如果不存在）
                if (!lockedDataRef.current[dataSourceKey]) {
                    lockedDataRef.current[dataSourceKey] = []
                }

                // 保存当前消息到缓存数组
                lockedDataRef.current[dataSourceKey].push({ mode, data })
                return
            }

            // 处理实时数据
            processLineData(chartXYRef, chartOption, mode, data, dataSourceKey)
        }
    })

    // 监听锁定状态变化
    useEffect(() => {
        // 当从锁定状态变为非锁定状态时，处理缓存的数据
        if (!isLocked && Object.keys(lockedDataRef.current).length > 0) {
            Object.entries(lockedDataRef.current).forEach(([dataSourceKey, messageArray]) => {
                // 按顺序处理缓存的消息
                messageArray.forEach(({ mode, data }) => {
                    processLineData(chartXYRef, chartOption, mode, data, dataSourceKey)
                })
            })

            // 清空缓存
            lockedDataRef.current = {}

            // 调用恢复 避免右击菜单误触拖动
            chartXYRef.current?.restore?.()
        }
    }, [isLocked, chartOption?.lines, chartXYRef])

    return {
        highlightLineId
    }
}

export default useData
