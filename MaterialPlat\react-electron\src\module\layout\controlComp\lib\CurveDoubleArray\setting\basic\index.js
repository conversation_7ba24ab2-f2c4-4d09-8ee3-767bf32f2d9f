import React, { useEffect, useMemo, useRef } from 'react'
import {
    Form, Select, InputNumber, Input, Checkbox, Row, Col, message
} from 'antd'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'

import VButton from '@/components/vButton/index'
import useDoubleArrayInputVariable from '@/hooks/project/inputVariable/useDoubleArrayInputVariable'
import useDoubleArrayListInputVariable from '@/hooks/project/inputVariable/useDoubleArrayListInputVariable'
import useNumberInputVariable from '@/hooks/project/inputVariable/useNumberInputVariable'
import useBufferInputVariable from '@/hooks/project/inputVariable/useBufferInputVariable'

import { SOURCE_TYPE, PROPORTION_TYPE, findOptions } from '../constants'
import { Style } from './style'

const { Item } = Form

const Basic = ({ channels, isBufferCurve }) => {
    const { t } = useTranslation()
    const unitList = useSelector(state => state.global.unitList)
    const form = Form.useFormInstance()

    const sourceType = Form.useWatch(['base', 'sourceType'], form)
    const xAxisSignal = Form.useWatch(['curveGroup', 'yAxis', 'xSignal'], form)
    const xAxisProportionType = Form.useWatch(['xAxis', 'proportionType'], form)
    const yAxisProportionType = Form.useWatch(['yAxis', 'proportionType'], form)
    const y2AxisProportionType = Form.useWatch(['y2Axis', 'proportionType'], form)

    const xAxisUnits = useMemo(() => {
        const dimensionId = channels?.find(f => f.code === xAxisSignal)?.dimensionId ?? { dimensionId: '' }
        return unitList.find(f => f.id === dimensionId)?.units ?? []
    }, [channels, unitList, xAxisSignal])

    const proportionTypeNoLimit = [
        PROPORTION_TYPE['数据范围'],
        PROPORTION_TYPE['扫描范围']
    ]

    return (
        <Style>
            <div>
                <Item
                    style={{ marginBottom: '0px' }}
                    name={['defineAxis', 'isDefineAxis']}
                    valuePropName="checked"
                >
                    <Checkbox>{t('启动变量绑定坐标源')}</Checkbox>
                </Item>
            </div>

            <div className="container-box">
                <div className="title">{t('X轴设置')}</div>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['curveGroup', 'yAxis', 'xSignal']}
                            label={t('通道')}
                        >
                            <Select
                                options={channels}
                                fieldNames={{ label: 'name', value: 'code' }}
                            />
                        </Item>
                    </Col>
                    <Col span={12}>
                        <Item
                            name={['xAxis', 'name']}
                            label={t('显示名称')}
                        >
                            <Input />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['xAxis', 'unit']}
                            label={t('单位')}
                        >
                            <Select
                                options={xAxisUnits}
                                fieldNames={{ label: 'name', value: 'id' }}
                            />
                        </Item>
                    </Col>
                    <Col span={12}>
                        <Item
                            name={['xAxis', 'proportionType']}
                            label={t('自动调整比例')}
                        >
                            <Select
                                options={findOptions(PROPORTION_TYPE)}
                            />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['xAxis', 'lowLimit']}
                            label={t('下限')}
                        >
                            <InputNumber
                                style={{ width: '100%' }}
                                disabled={proportionTypeNoLimit.includes(xAxisProportionType)}
                            />
                        </Item>
                    </Col>
                    <Col span={12}>
                        <Item
                            name={['xAxis', 'upLimit']}
                            label={t('上限')}
                        >
                            <InputNumber
                                style={{ width: '100%' }}
                                disabled={proportionTypeNoLimit.includes(xAxisProportionType)}
                            />
                        </Item>
                    </Col>
                </Row>

                <Row>
                    <Col span={12}>
                        <Item
                            name={['xAxis', 'lastRange']}
                            label={t('扫描范围')}
                        >
                            <InputNumber
                                style={{ width: '100%' }}
                                disabled={xAxisProportionType !== PROPORTION_TYPE['扫描范围']}
                            />
                        </Item>
                    </Col>
                    <Col span={12}>
                        <Item
                            name={['xAxis', 'isLog']}
                            label={t('对数')}
                            valuePropName="checked"
                        >
                            <Checkbox />
                        </Item>
                    </Col>
                </Row>
            </div>

            <div className="container-box">
                <div className="title">{t('Y1轴设置')}</div>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['curveGroup', 'yAxis', 'ySignal']}
                            label={t('通道')}
                        >
                            <Select
                                mode="multiple"
                                options={channels}
                                maxCount={sourceType === SOURCE_TYPE.多数据源 ? 1 : undefined}
                                fieldNames={{ label: 'name', value: 'code' }}
                            />
                        </Item>
                    </Col>
                    <Col span={12}>
                        <Item
                            name={['yAxis', 'name']}
                            label={t('显示名称')}
                        >
                            <Input />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['yAxis', 'proportionType']}
                            label={t('自动调整比例')}
                        >
                            <Select
                                options={findOptions(PROPORTION_TYPE)}
                            />
                        </Item>
                    </Col>
                    <Col span={12}>
                        <Item
                            name={['yAxis', 'lowLimit']}
                            label={t('下限')}
                        >
                            <InputNumber
                                style={{ width: '100%' }}
                                disabled={proportionTypeNoLimit.includes(yAxisProportionType)}
                            />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['yAxis', 'upLimit']}
                            label={t('上限')}
                        >
                            <InputNumber
                                style={{ width: '100%' }}
                                disabled={proportionTypeNoLimit.includes(yAxisProportionType)}
                            />
                        </Item>
                    </Col>
                    <Col span={12}>
                        <Item
                            name={['yAxis', 'lastRange']}
                            label={t('扫描范围')}
                        >
                            <InputNumber
                                style={{ width: '100%' }}
                                disabled={yAxisProportionType !== PROPORTION_TYPE['扫描范围']}
                            />
                        </Item>
                    </Col>
                </Row>

                <Row>
                    <Col span={12}>
                        <Item
                            name={['yAxis', 'isLog']}
                            label={t('对数')}
                            valuePropName="checked"
                        >
                            <Checkbox />
                        </Item>
                    </Col>
                </Row>
            </div>

            <div className="container-box">
                <div className="title">{t('Y2轴设置')}</div>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['curveGroup', 'y2Axis', 'ySignal']}
                            label={t('通道')}
                        >
                            <Select
                                mode="multiple"
                                options={channels}
                                maxCount={sourceType === SOURCE_TYPE.多数据源 ? 1 : undefined}
                                fieldNames={{ label: 'name', value: 'code' }}
                            />
                        </Item>
                    </Col>
                    <Col span={12}>
                        <Item
                            name={['y2Axis', 'name']}
                            label={t('显示名称')}
                        >
                            <Input />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['y2Axis', 'proportionType']}
                            label={t('自动调整比例')}
                        >
                            <Select
                                options={findOptions(PROPORTION_TYPE)}
                            />
                        </Item>
                    </Col>
                    <Col span={12}>
                        <Item
                            name={['y2Axis', 'lowLimit']}
                            label={t('下限')}
                        >
                            <InputNumber
                                style={{ width: '100%' }}
                                disabled={proportionTypeNoLimit.includes(y2AxisProportionType)}
                            />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['y2Axis', 'upLimit']}
                            label={t('上限')}
                        >
                            <InputNumber
                                style={{ width: '100%' }}
                                disabled={proportionTypeNoLimit.includes(y2AxisProportionType)}
                            />
                        </Item>
                    </Col>
                    <Col span={12}>
                        <Item
                            name={['y2Axis', 'lastRange']}
                            label={t('扫描范围')}
                        >
                            <InputNumber
                                style={{ width: '100%' }}
                                disabled={y2AxisProportionType !== PROPORTION_TYPE['扫描范围']}
                            />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['y2Axis', 'isLog']}
                            label={t('对数')}
                            valuePropName="checked"
                        >
                            <Checkbox />
                        </Item>
                    </Col>
                </Row>
            </div>
        </Style>
    )
}

export default Basic
