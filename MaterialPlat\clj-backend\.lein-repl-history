()
(start)
(reset-db)
(stop)
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
exit
(start)
(reset-db)
(restart)
exit
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
exit
(restart)
exit
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
CD
Cc
(restart)
exit
(start)
(reset-db)
(start)
(reset-db)
(start)
(reset-db)
(start)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(restart)、
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
clear
if ($env:TERM_PROGRAM -eq "vscode") { $Global:__TraeInjectAgain=1; try { . "c:\Users\<USER>\AppData\Local\Programs\Trae\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch{} }; Write-Output "[Trae] Shell integration is not enabled, try to fix it now."
clear
lein check
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
clear
if ($env:TERM_PROGRAM -eq "vscode") { $Global:__TraeInjectAgain=1; try { . "c:\Users\<USER>\AppData\Local\Programs\Trae\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch{} }; Write-Output "[Trae] Shell integration is not enabled, try to fix it now."
clear
lein check
(restart)
clear
if ($env:TERM_PROGRAM -eq "vscode") { $Global:__TraeInjectAgain=1; try { . "c:\Users\<USER>\AppData\Local\Programs\Trae\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch{} }; Write-Output "[Trae] Shell integration is not enabled, try to fix it now."
clear
lein check
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
  
(restart)
  
clear
if ($env:TERM_PROGRAM -eq "vscode") { $Global:__TraeInjectAgain=1; try { . "c:\Users\<USER>\AppData\Local\Programs\Trae\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch{} }; Write-Output "[Trae] Shell integration is not enabled, try to fix it now."
clear
lein check
(restart)
(reset-db)
(restart)
clear
if ($env:TERM_PROGRAM -eq "vscode") { $Global:__TraeInjectAgain=1; try { . "c:\Users\<USER>\AppData\Local\Programs\Trae\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch{} }; Write-Output "[Trae] Shell integration is not enabled, try to fix it now."
clear
lein check
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
(reset-db)
(restart)
