import React, {
    useState,
    useLayoutEffect,
    useRef,
    useImperativeHandle,
    forwardRef
} from 'react'
import { Form } from 'antd'
import { useTranslation } from 'react-i18next'

import VTransfer from '@/components/vTransfer'
import useAuxiliaryLine from '@/hooks/useAuxiliaryLine'

const { Item } = Form

/**
 * 第六步，辅助线
 */
const StepAuxiliary = forwardRef(({ isBufferCurve }) => {
    const { t } = useTranslation()
    const { auxiliaryLineArrayList, auxiliaryLineSignalList } = useAuxiliaryLine()

    const form = Form.useFormInstance()

    return (
        <div className="step-auxiliary">
            <Item
                noStyle
                name={['auxiliary']}
                valuePropName="targetKeys"
            >
                <VTransfer
                    dataSource={isBufferCurve ? auxiliaryLineSignalList : auxiliaryLineArrayList}
                    onChangeDelWay={(data) => {
                        const keys = form.getFieldValue('auxiliary')
                        form.setFieldValue('auxiliary', keys.filter(f => f !== data.id))
                    }}
                    render={(item) => item.name}
                    oneWayLabel="name"
                    oneWay
                />
            </Item>
        </div>
    )
})

export default StepAuxiliary
