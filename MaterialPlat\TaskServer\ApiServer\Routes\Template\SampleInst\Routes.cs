using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ScriptEngine.SampleInst;
using Scripting;
using TaskServer.ApiServer.Routes.Template.Interface;

/*
 * 接口实例化试样:
 * + 批量新增或编辑试样: 对应前端 新增 编辑 批量新增或编辑
 * + 批量删除试样: 对应前端 删除 批量删除
 * + 选中试样: 对应前端 选中 多选
 */

public record BatchCreateOrUpdateParams(string ClassName, Dictionary<string, SampleInstDTO> CodeAndSampleInsts);

public record BatchDeleteParams(string ClassName, List<string> Codes);
public record SelectParams(string ClassName, string CurrentInstCode, string[]? SelectedInstCodes);
public record ChangeStateParams(string ClassName, string State);

/// <summary>
/// 实例化试样相关接口
/// </summary>
[ApiController]
[Route("/template/sample-inst")]
public class Routes
{
    /// <summary>
    /// 批量修改或编辑试样
    /// </summary>
    [HttpPost]
    [Route("/template/sample-inst")]
    public IResult BatchCreateOrUpdateSampleInst(BatchCreateOrUpdateParams param)
    {
        var template = ITemplate.GetTemplateByName(param.ClassName);
        template?.BatchCreateOrUpdateSampleInst(param.CodeAndSampleInsts);
        return Results.Ok("Success");
    }
    
    /// <summary>
    /// 修改当前选中试样
    /// </summary>
    [HttpPost]
    [Route("/template/sample-inst/select")]
    public IResult SelectSampleInst(SelectParams param)
    {
        var template = ITemplate.GetTemplateByName(param.ClassName);
        template?.SelectSampleInsts(param.CurrentInstCode, param.SelectedInstCodes);
        //UISubscriptionController.RestartTemplateSubscription(param.ClassName);//先加回来等待前端改
        return Results.Ok("Success");
    }
    
    /// <summary>
    /// 修改当前试样实例状态
    /// </summary>
    [HttpPost]
    [Route("/template/sample-inst/current/state")]
    public IResult ChangeCurrentInstState(ChangeStateParams param)
    {
        var template = ITemplate.GetTemplateByName(param.ClassName);
        template?.ChangeSampleInstState(param.State);
        return Results.Ok("Success");
    }
    
    /// <summary>
    /// 批量删除试样
    /// </summary>
    [HttpDelete]
    [Route("/template/sample-inst")]
    public IResult DeleteSampleInst([FromBody] BatchDeleteParams param)
    {
        var template = ITemplate.GetTemplateByName(param.ClassName);
        template?.BatchDeleteSampleInsts(param.Codes);
        return Results.Ok("Success");
    }
}