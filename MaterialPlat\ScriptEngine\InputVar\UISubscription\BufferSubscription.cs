using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ScriptEngine.InputVar.InputVars;
using MQ;
using FuncLibs;
using static Scripting.ITemplate;
using Scripting;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing.Charts;
using NPOI.SS.Formula.Functions;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Diagnostics;

namespace ScriptEngine.InputVar.UISubscription
{
    /// <summary>
    /// Buffer订阅实现类
    /// 负责单个订阅的生命周期管理、定时器控制和数据推送
    /// </summary>
    public class BufferSubscription : Subscription
    {
        //buffer输入变量
        private BufferInputVar _bufferInputVar => (BufferInputVar)_InputVar;
        private bool ISDbGet = true;
        private int lastTail = -1;
        //已推送的数据库条数
        private int lastCount = -1;

        public BufferSubscription(SubscriptionRequest request) : base(request)
        {
            if (_request.TestStatus == 0&& _request.DataCodes.Contains("index")==false)
            {
                _request.DataCodes.Add("index");

            }
            if (_request.TestStatus == 1 && _request.Number <= 0 )
            {
                _model = 1;
            }
            else
            {
                _model = 0;
            }
            SendToUI(new Dictionary<string, double[]>(), 0, _template.CurrentInst.Code);
        }

        /// <summary>
        /// 恢复订阅,重写拿db中暂停到现在的数据
        /// </summary>
        public override void Resume()
        {
            base.Resume();
        }
        //buffer被清空
        public void bufferResetCallback()
        {
            //如果在获取历史数据用ISDbGet控制停止推送，停止定时器继续推送，重置定时器状态
            ISDbGet = false;
            _timer?.Change(Timeout.Infinite, Timeout.Infinite);
            _status = SubscriptionStatus.Paused;
            //与定时器使用同一个锁对象来控制线程安全
            lock (_lockObject)
            {
                SendToUI(new Dictionary<string, double[]>(), 0, _template.CurrentInst.Code);
                _isFirstPush = true;
                lastTail = -1;
                //已推送的数据库条数
                lastCount = -1;
                _status = SubscriptionStatus.Running;
                _timer?.Change(0, _request.Timer);
            }
        }
        Stopwatch stopwatchtotalFlowSubscription = new Stopwatch();
        int stopwatchtotalFlowSubscription_i = 0;
        /// <summary>
        /// 定时器回调方法
        /// </summary>
        public override void OnTimerCallback(object? start)
        {
            try
            {
                if (stopwatchtotalFlowSubscription_i % 100 == 0)
                {
                    stopwatchtotalFlowSubscription.Stop();
                    stopwatchtotalFlowSubscription_i = 0;
                    Logger.Error($"UI控件{_request.ControlCompId}  timer每次理论间隔{_request.Timer}，100次推送间隔：{stopwatchtotalFlowSubscription.ElapsedMilliseconds}ms，总推送数量：{sumDataCount}");
                    sumDataCount = 0;
                    stopwatchtotalFlowSubscription.Restart();
                }
                stopwatchtotalFlowSubscription_i++;
                OnTimerCallbackService();
             }
            catch (Exception ex)
            {
                
                Logger.Error($"UI控件{_request.ControlCompId} ,OnTimerCallback异常："+ex);
            }
        }
        void OnTimerCallbackService()
        {
            if (!nextPush)
            {
                return;
            }
            lock (_lockObject)
            {
                if (!nextPush)
                {
                    return;
                }
                nextPush = false;
                if (_status != SubscriptionStatus.Running)
                {
                    return;
                }
                //如果模式为0则每次推送刷新_isFirstPush
                if (_model == 0)
                {
                    _isFirstPush = true;
                }
                if (_request.Number > 0)
                {
                    var data = GetLatestNumberData();
                    if (data != null && data.Count > 0)
                    {
                        PushData(data, _template.CurrentInst.Code);
                    }
                }
                else if (_request.TestStatus == 0)
                {
                    //非试验状态恢复不推送直接暂停
                    if (_isResume)
                    {
                        Pause();
                        _isResume = false;
                    }
                    else
                    {
                        SendToUIStart();
                        GetHistoricalDataAndPushData();
                        Pause();
                        SendToUIStop();
                    }
                }
                else
                {
                    if (ISDbGet)
                    {
                        GetHistoricalDataAndPushData();
                        ISDbGet = false;
                    }
                    else
                    {
                        //试验状态下数据
                        var data = GetSubscriptionData();
                        if (data != null && data.Count > 0)
                        {
                            PushData(data, _template.CurrentInst.Code);
                        }
                    }
                }
                nextPush = true;
            }
        }
        /// <summary>
        /// 获取订阅数据
        /// </summary>
        private Dictionary<string, double[]> GetSubscriptionData()
        {
            if (_bufferInputVar == null)
            {
                return null;
            }

            try
            {

                if (_isResume)
                {

                    return GetResumeData();
                }
                else
                {
                    return GetRealTimeData();
                }

            }
            catch (Exception ex)
            {
                Logger.Error($"获取订阅数据失败: {_request.ControlCompId}, 错误: {ex}");
                return null;
            }
        }

        //动态获取新增的数据
        private Dictionary<string, double[]> GetRealTimeData()
        {
            var result = new Dictionary<string, double[]>();


            int newTail = _bufferInputVar.GetTail(_request.DataCodes[0]);

            //数据无更新
            if (lastTail == newTail)
            {
                return result;

            }
            int BufferSize = _bufferInputVar.Value[_request.DataCodes[0]].GetBufferSize();
            int TopIndex = lastTail + 1;
            lastTail = newTail;
            if (TopIndex > BufferSize - 1)
            {
                TopIndex = 0;
            }
            foreach (var signalCode in _request.DataCodes)
            {
                if (_bufferInputVar.Value.ContainsKey(signalCode))
                {
                    var buffer = _bufferInputVar.Value[signalCode];
                    var data = lastTail == -1 ? buffer.GetBuffer() : buffer.GetBuffer(TopIndex, newTail);
                    result.Add(signalCode, data);

                }
            }
            lastCount = _template.Db.GetHistoricalDataCount(_template.CurrentInst.Code + _bufferInputVar.Code);
            return result;
        }

        /// <summary>
        /// 获取最近n条数据
        /// </summary>
        private Dictionary<string, double[]> GetLatestNumberData()
        {
            var result = new Dictionary<string, double[]>();
            // 获取每个信号的最新数据
            foreach (var signalCode in _request.DataCodes)
            {
                if (_bufferInputVar.Value.ContainsKey(signalCode))
                {
                    var buffer = _bufferInputVar.Value[signalCode];
                    var tailIndex = _bufferInputVar.GetTail(signalCode);
                    var data = buffer.GetBuffer();
                    // 获取最新的N个数据点
                    if (data.Length > _request.Number)
                    {
                        data = data.Skip(data.Length - _request.Number).ToArray();
                    }
                    result.Add(signalCode, data);
                }
            }
            return result;
        }
        //只要过程中ISDbGet为false则立刻停止后续推送
        private void GetHistoricalDataAndPushData()
        {
            if (!ISDbGet)
            {
                return;
            }
            //DB数据||多试样
            foreach (var item in _request.DaqCurveSelectedSampleCodes)
            {
                try
                {
                    if (!ISDbGet)
                    {
                        return;
                    }
                    int count = _template.Db.GetHistoricalDataCount(item + _bufferInputVar.Code);
                    if (count <= 0)
                    {
                        //没有历史数据，就拿内存中的数据，有一些临时数据不存库（高频曲线）
                        PushData(GetRealTimeData(), _template.CurrentInst.Code);
                        continue;
                    }
                    int totalPages = (int)Math.Ceiling((double)count / MAXNUMBER);
                    for (int i = 0; i < totalPages; i++)
                    {
                        if (!ISDbGet)
                        {
                            return;
                        }
                        var PushDAta = GetHistoricalData(item, i, MAXNUMBER);
                        if (PushDAta != null && PushDAta.Count > 0)
                        {
                            if (!ISDbGet)
                            {
                                return;
                            }
                            PushData(PushDAta, item);
                        }
                    }
                    lastCount = _template.Db.GetHistoricalDataCount(item + _bufferInputVar.Code);
                    //如果数据库后续数量大于了本次查询，则LastCount设置为本次查询数量
                    if (lastCount > totalPages * MAXNUMBER)
                    {
                        lastCount = totalPages * MAXNUMBER;
                    }
                    lastTail = _bufferInputVar.GetTail(_request.DataCodes[0]);
                }
                catch (Exception ex)
                {
                    Logger.Error($"获取历史数据失败: {_request.ControlCompId}, 错误: {ex}");
                }
            }
        }
        /// <summary>
        /// 获取历史数据
        /// </summary>
        private Dictionary<string, double[]> GetHistoricalData(string SampleCode, int pageIndex, int pageSize )
        {
            var result = new Dictionary<string, double[]>();
            // 使用数据库获取历史数据
            if (_template?.Db != null)
            {
                try
                {
                    var historicalData = _template.Db.GetHistoricalDataPaged(
                        SampleCode + _bufferInputVar.Code,
                         _request.DataCodes.ToArray(),
                         pageIndex, pageSize
                    );
                    result = DataSerialization(historicalData);
                    return result;
                }
                catch (Exception ex)
                {
                    Logger.Error($"获取历史数据失败: {_request.ControlCompId}, 错误: {ex}");
                }
            }

            return result;
        }
        private Dictionary<string, double[]> GetResumeData()
        {
            string tablename = _template.CurrentInst.Code + _bufferInputVar.Code;
            int newCount = _template.Db.GetHistoricalDataCount(tablename);
            //newCount数据库中没数据，可能是不存库只做末尾范围展示的（高频实时曲线）
            if (newCount == 0)
            {
                //数据库中没有，那就拿现在内存的数据。
                lastTail = -1;
                SendToUI(new Dictionary<string, double[]>(), 0, _template.CurrentInst.Code);
                //拿内存中的现有数据
                return GetRealTimeData();
            }
            if (newCount - lastCount > MAXNUMBER)
            {
                var historicalData = _template.Db.GetHistoricalDataOnlyDouble(
                        tablename,
                        _request.DataCodes,
                        lastCount, lastCount + MAXNUMBER
                    );
                Dictionary<string, double[]> data = DataSerialization(historicalData);
                lastCount = lastCount + MAXNUMBER;
                return data;
            }
            else
            {
                var historicalData = _template.Db.GetHistoricalDataOnlyDouble(
                              tablename,
                              _request.DataCodes,
                              lastCount, newCount
                          );
                Dictionary<string, double[]> data = DataSerialization(historicalData);
                lastTail = _bufferInputVar.GetTail(_request.DataCodes[0]);
                lastCount = newCount;
                _isResume = false;
                return data;
            }
        }
          private Dictionary<string, double[]> DataSerialization(IEnumerable<double[]> historicalData)
        {
            var tempDict = new Dictionary<string, List<double>>();
            foreach (var item in _request.DataCodes)
            {
                tempDict.Add(item, new List<double>());
            }

            foreach (var row in historicalData)
            {
                for (int i = 0; i < _request.DataCodes.Count; i++)
                {
                    tempDict[_request.DataCodes[i]].Add(row[i]);
                }
            }
            var data = tempDict.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value.ToArray()
            );
            return data;
        }
        private Dictionary<string, double[]> DataSerialization(IEnumerable<object[]> historicalData)
        {
            var tempDict = new Dictionary<string, List<double>>();
            foreach (var item in _request.DataCodes)
            {
                tempDict.Add(item, new List<double>());
            }

            foreach (var row in historicalData)
            {
                for (int i = 0; i < _request.DataCodes.Count; i++)
                {
                    tempDict[_request.DataCodes[i]].Add(Convert.ToDouble(row[i]));
                }
            }
            var data = tempDict.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value.ToArray()
            );
            return data;
        }

        /// <summary>
        /// 推送数据
        /// </summary>
        public void PushData(Dictionary<string, double[]> data, string stringsampleCode = null)
        {
            // 检查 data 是否为空或没有元素
            if (data == null || data.Count == 0)
            {
                return;
            }
            int SentIndex = 0;
            while (SentIndex <= data.First().Value.Length - 1)
            {
                var sendData = new Dictionary<string, double[]>();
                foreach (var item in data)
                {
                    if (item.Value.Length > SentIndex)
                    {
                        sendData.Add(item.Key, item.Value.Skip(SentIndex).Take(MAXPUSH).ToArray());
                    }
                }
                SentIndex += MAXPUSH;
                if (sendData.Count > 0)
                {
                    if (_isFirstPush == true)
                    {
                        _isFirstPush = false;
                        SendToUI(sendData, 0, stringsampleCode);
                    } else
                    {
                        SendToUI(sendData, 1, stringsampleCode);
                    }
                }
                SentIndex++;
            }
        }
        int sumDataCount = 0;
        public void SendToUI(Dictionary<string, double[]> data, int model, string sampleCode)
        {
            if (data.Count > 0)
            {
                sumDataCount += data.Values.First().Length;
            }
            SubscriptionTOUIBufferData subscriptionTOUIData = new SubscriptionTOUIBufferData()
            {
                mode = model, // 更新模式：0 - 全量更控件之前数据丢弃    1 - 增量 控件之前数据保留
                sampleCode = sampleCode,//试样code
                doubleArrayIndex = 0, // 二维数组对应集合中的下标  为了二维数组集合区分数据来源,buffer不需要
                controlCompId = Request.ControlCompId, // 控件id - 考虑放到topic中 避免无用消耗
                totalCount = 0,// 当前数据总量 - 主要用在二维数组表格页码显示，buffer不需要
                data = data,
            };
            byte[] retmessage = Consts.MessagePackSerializer.Serialize(
                   subscriptionTOUIData
               );
            ISystemBus.SendToUISubscriptionData(retmessage, Request.TemplateName!, Request.ControlCompId);
        }
    }
}