using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FuncLibs;
using MQ;
using ScriptEngine.InputVar.InputVars;

namespace ScriptEngine.InputVar.UISubscription
{
    public class DoubleArraySubscription : Subscription
    {
        //buffer输入变量
        private DoubleArray _doubleArray;
        private int _totalCount => _doubleArray.CurrentRowCount;
        private int LastIndex = -1;
        private int _doubleArrayIndex = 0;
        public DoubleArraySubscription(SubscriptionRequest request) : base(request)
        {
            _doubleArray = ((DoubleArrayInputVar)_InputVar).Value;
        }

        /// <summary>
        /// 将JsonElement转换为合适的.NET类型
        /// </summary>
        /// <param name="jsonElement">要转换的JsonElement</param>
        /// <returns>转换后的值</returns>
        private object ConvertJsonElement(JsonElement jsonElement)
        {
            try
            {
                return jsonElement.ValueKind switch
                {
                    JsonValueKind.Number =>
                        jsonElement.TryGetInt32(out var intVal) ? intVal : jsonElement.GetDouble(),
                    JsonValueKind.String => jsonElement.GetString() ?? string.Empty,
                    JsonValueKind.True => true,
                    JsonValueKind.False => false,
                    JsonValueKind.Null => null,
                    _ => jsonElement.ToString()
                };
            }
            catch (Exception ex)
            {
                Logger.Error($"JsonElement转换失败: {ex.Message}");
                return jsonElement.ToString();
            }
        }
        /// <summary>
        /// 来自二维数组集合的订阅
        /// </summary>
        /// <param name="doubleArray"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        public DoubleArraySubscription(DoubleArray doubleArray, SubscriptionRequest request, int doubleArrayIndex)
            : base(request)
        {
            _doubleArray = doubleArray;
            _doubleArrayIndex = doubleArrayIndex;
        }

        /// <summary>
        /// 启动订阅
        /// </summary>
        public override void Start()
        {
            ClearUI();
            _doubleArray.ClearDoubleArrayValueCallbacks.Add(ClearUIDate);
            _doubleArray.SendToUIClear.Add(ClearUI);
            if (_request.Timer < 0)
            {
                //主动推送一次
                Task.Run(() => SendToUICallback());
                // 主动触发的都要全量刷新
                _request.TestStatus = 0;
                //将OnTimerCallback回调添加进二维数组中
                _doubleArray.SendToUICallbacks.Add(SendToUICallback);
            }
            else
            {
                base.Start();
            }
        }
        bool clearUI = true;
        /// <summary>
        /// 下一条数据清空UI
        /// </summary>
        public void ClearUIDate()
        {
            lock (_lockObject)
            {
                clearUI = true;
                LastIndex = -1;
            }
        }
        /// <summary>
        /// 清空UI现有数据
        /// </summary>
        public void ClearUI()
        {
            lock (_lockObject)
            {
                SubscriptionTOUIDoubleAyyayData subscriptionTOUIData = new SubscriptionTOUIDoubleAyyayData()
                {
                    mode = 0, // 更新模式：0 - 全量更控件之前数据丢弃    1 - 增量 控件之前数据保留
                    sampleCode = _template.CurrentInst.Code,
                    doubleArrayIndex = _doubleArrayIndex, // 二维数组对应集合中的下标  为了二维数组集合区分   数据来源
                    controlCompId = Request.ControlCompId, // 控件id - 考虑放到topic中 避免无用消耗
                    totalCount = 0,// 当前数据总量 - 主要用在二维数组表格页码显示，曲线不用
                    data = new Dictionary<string, object[]>()
                };
                byte[] retmessage = Consts.MessagePackSerializer.Serialize(
                       subscriptionTOUIData
                   );
                ISystemBus.SendToUISubscriptionData(retmessage, Request.TemplateName!, Request.ControlCompId);
            }
        }

        void SendToUICallback()
        {
            lock (_lockObject)
            {
                var retDAta = GetAllData();
                Logger.Error($"UI控件{_request.ControlCompId} ,触发手动推送，条数: {retDAta.Values.First().Length}");
                PushData(retDAta);
            }
        }
        public override void Stop()
        {
            _doubleArray.SendToUIClear.Remove(ClearUI);
            _doubleArray.SendToUICallbacks.Remove(SendToUICallback);
            _doubleArray.ClearDoubleArrayValueCallbacks.Remove(ClearUIDate);
            base.Stop();
        }
        
        Stopwatch stopwatchtotalFlowSubscription = new Stopwatch();
        int stopwatchtotalFlowSubscription_i = 0;
        public override void OnTimerCallback(object? start)
        {
            try
            {
                if (stopwatchtotalFlowSubscription_i % 100 == 0)
                {
                    stopwatchtotalFlowSubscription.Stop();
                    stopwatchtotalFlowSubscription_i = 0;
                    Logger.Error($"UI控件{_request.ControlCompId}  timer每次理论间隔{_request.Timer}，100次推送间隔：{stopwatchtotalFlowSubscription.ElapsedMilliseconds}ms，总推送数量：{sumDataCount}");
                    sumDataCount = 0;
                    stopwatchtotalFlowSubscription.Restart();
                }
                OnTimerCallback();
                stopwatchtotalFlowSubscription_i++;
            }
            catch (Exception ex)
            {

                Logger.Error($"UI控件{_request.ControlCompId} , OnTimerCallback异常：" + ex);
            }
        }
        /// <summary>
        /// 定时器回调方法
        /// </summary>
        public void OnTimerCallback()
        {
          
            if (!nextPush)
            {
                return;
            }
            lock (_lockObject)
            {
                if (!nextPush)
                {
                    return;
                }
                nextPush = false;
                if (_status != SubscriptionStatus.Running)
                {
                    return;
                }
                //只要有固定数量始终推送新值
                if (_request.Number > 0)
                {
                    var retDAta = GetLatestNumberData(Request.Number);
                    PushData(retDAta);
                }
                else if (_request.TestStatus == 0)
                {
                    if (_isResume)
                    {
                        Pause();
                        _isResume = false;
                    }
                    else
                    {
                        SendToUIStart();
                        var retDAta = GetAllData();
                        PushData(retDAta);
                        Pause();
                        SendToUIStop();
                    }
                }
                else
                {
                    var data = GetSubscriptionData();
                    if (data != null && data.Count > 0)
                    {
                        PushData(data);
                    }
                }
                nextPush = true;
            }

        }

        /// <summary>
        /// 获取订阅数据
        /// </summary>
        private Dictionary<string, object[]> GetSubscriptionData()
        {
            if (_doubleArray == null)
            {
                return null;
            }

            try
            {
                return GetRealTimeData();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取订阅数据失败: {_request.ControlCompId}, 错误: {ex}");
                return null;
            }
        }

        /// <summary>
        /// 获取全部数据
        /// </summary>
        private Dictionary<string, object[]> GetAllData()
        {
            var result = new Dictionary<string, object[]>();
            foreach (var item in Request.DataCodes)
            {
                var data = _doubleArray.GetColumnObject(item).Values;
                result.Add(item, data.ToArray());
            }
            int count = result.First().Value.Length;
            var indexs = new object[count];
            for (int i = 0; i < count; i++)
            {
                indexs[i] = i;
            }
            result.Add("index", indexs);
            return result;
        }

        /// <summary>
        /// 获取最近n条数据
        /// </summary>
        private Dictionary<string, object[]> GetLatestNumberData(int Number)
        {
            //缺少一列下标
            int qishi1 = 0;
            var result = new Dictionary<string, object[]>();
            foreach (var item in Request.DataCodes)
            {
                var data = _doubleArray.GetColumnObject(item).Values;
                if (data.Count >= Number)
                {
                    qishi1 = data.Count - Number;

                    result.Add(item, data.Skip(data.Count - Number).ToArray()); ;
                }
                else
                {
                    Number = data.Count;
                    result.Add(item, data.ToArray());
                }

            }
            var indexs = new object[Number];
            for (int i = 0; i < Number; i++)
            {
                indexs[i] = qishi1 + i;
            }
            result.Add("index", indexs);

            return result;
        }

        /// <summary>
        /// 获取实时
        /// </summary>
        private Dictionary<string, object[]> GetRealTimeData()
        {
            var result = new Dictionary<string, object[]>();
            if (LastIndex == -1)
            {
                //获取所有数据
                result = GetAllData();
            }
            else
            {
                //发送模式为0 时，全量推送
                if (_InputVar.Type == "DoubleArray" && ((DoubleArrayInputVar)_InputVar).SendMode == 0)
                {
                    _model = 0;
                    result = GetAllData();
                }
                else if (_InputVar.Type == "DoubleArrayList" && ((DoubleArrayListInputVar)_InputVar).SendMode == 0)
                {
                    _model = 0;
                    result = GetAllData();
                }
                else
                {
                    _model = 1;
                    //获取新增的数据
                    result = GetLatestNumberData(_totalCount - LastIndex);
                }
            }
            LastIndex = _totalCount;
            return result;
        }
        /// <summary>
        /// 推送数据
        /// </summary>
        public void PushData(Dictionary<string, object[]> data)
        {
            // 检查 data 是否为空或没有元素
            if (data == null || data.Count == 0)
            {
                return;
            }
            int SentIndex = 0;
            bool isFirstPush = false;
            while (SentIndex <= data.First().Value.Length - 1)
            {
                var sendData = new Dictionary<string, object[]>();
                foreach (var item in data)
                {
                    if (item.Value.Length > SentIndex)
                    {
                        sendData.Add(item.Key, item.Value.Skip(SentIndex).Take(MAXPUSH).ToArray());
                    }
                }
                SentIndex += MAXPUSH;
                if (sendData.Count > 0)
                {
                    if (_model == 0 && isFirstPush == false)
                    {
                        isFirstPush = true;
                        SendToUI(sendData, 0);
                    }
                    else
                    {
                        SendToUI(sendData, 1);
                    }

                }
                SentIndex++;
            }
        }
        int sumDataCount = 0;
        public void SendToUI(Dictionary<string, object[]> data, int model)
        {
            if (clearUI == true)
            {
                model = 0;
                clearUI = false;
            }
            if (data.Count > 0)
            {
                sumDataCount += data.Values.First().Length;
            }
            SubscriptionTOUIDoubleAyyayData subscriptionTOUIData = new SubscriptionTOUIDoubleAyyayData()
            {
                mode = model, // 更新模式：0 - 全量更控件之前数据丢弃    1 - 增量 控件之前数据保留
                sampleCode = _template.CurrentInst.Code,
                doubleArrayIndex = _doubleArrayIndex, // 二维数组对应集合中的下标  为了二维数组集合区分   数据来源
                controlCompId = Request.ControlCompId, // 控件id - 考虑放到topic中 避免无用消耗
                totalCount = _totalCount,// 当前数据总量 - 主要用在二维数组表格页码显示，曲线不用
                data = new Dictionary<string, object[]>()
            };
            foreach (var item in data.Keys)
            {
                var arr = data[item];

                // 逐元素检查和转换，处理混合类型数组
                var convertedElements = new List<object>();

                // 直接处理数组元素，逐一转换JsonElement，保留非JsonElement元素
                foreach (var element in arr)
                {
                    if (element is JsonElement je)
                    {
                        // JsonElement类型转换
                        var convertedValue = ConvertJsonElement(je);
                        convertedElements.Add(convertedValue);
                    }
                    else
                    {
                        // 非JsonElement类型直接保留
                        convertedElements.Add(element);
                    }
                }

                subscriptionTOUIData.data.Add(item, convertedElements.ToArray());
            }
            byte[] retmessage = Consts.MessagePackSerializer.Serialize(
                   subscriptionTOUIData
               );
            ISystemBus.SendToUISubscriptionData(retmessage, Request.TemplateName!, Request.ControlCompId);
        }

    }
}