export const steps = ['曲线图', '曲线组', 'X-轴', 'Y-轴1', 'Y-轴2', '辅助线', '标签', '标记点设置', '定义坐标源']

export const SOURCE_TYPE = {
    单数据源: 'single',
    多数据源: 'multi'
}
export const LINE_STYLE_TYPE = {
    '——————': 'solid',
    '------': 'dashed',
    '......': 'dotted'
}

export const PROPORTION_TYPE = {
    上下限范围: 'not',
    正向位置扩展: 'extend',
    负向位置扩展: 'min-extend',
    应用范围: 'all',
    数据范围: 'data-range',
    扫描范围: 'last-range'
}

export const SIGN_STYLE_TYPE = {
    o: 'o',
    '▽': '▽',
    '□': '□'
}

export const INTERVAL_TYPE = {
    线性: 'linear',
    对数: 'logarithmic',
    等间隔: 'equalInterval'
}

export const findOptions = (source) => {
    return Object.entries(source).map(([label, value]) => ({ label, value }))
}

export const CURVE_STYLE = {
    isLine: true,
    lineType: LINE_STYLE_TYPE['——————'],
    lineThickness: 2,
    isSign: false,
    signType: SIGN_STYLE_TYPE.o,
    signEach: false,
    color: '#000000',
    code: '',
    isApply: false
}
