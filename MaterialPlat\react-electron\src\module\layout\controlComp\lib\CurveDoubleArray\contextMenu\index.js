/* eslint-disable new-cap */
import React, { memo } from 'react'
import { useSelector } from 'react-redux'
import { jsPDF } from 'jspdf'

import RightMenu from '@/components/contextMenu2/index'
import useCopy from '@/hooks/useCopy'
import { COPY_TYPE } from '@/utils/constants'

const ContextMenu = ({
    rightClickType,
    domId,
    setOpen,
    layoutConfig,
    openCross, setOpenCross,
    openBreak, setOpenBreak,
    showPointTag, setShowPointTag,
    showChunkTag, setShowChunkTag,
    onRestore,
    onClearBreakPoint,
    isLocked, setIsLocked,
    isMarking, onActivateMarking, onStopMarking
}) => {
    const openExperiment = useSelector(state => state.subTask.openExperiment)
    const { copy } = useCopy()

    // 打印曲线图
    const printCurve = () => {
        const dom = document.getElementById(domId)
        const domCanvas = dom.querySelector('canvas')
        console.dir(domCanvas)
        const pdf = new jsPDF({ orientation: 'l' })
        pdf.addImage(domCanvas.toDataURL('image/jpg'), 'PNG', 0, 0, pdf.internal.pageSize.getWidth(), pdf.internal.pageSize.getHeight())
        pdf.save('curve.pdf')
    }

    const copyClipboard = () => {
        const dom = document.getElementById(domId)
        const domCanvas = dom.querySelector('canvas')
        domCanvas.toBlob(blob => {
            copy(blob, COPY_TYPE.图片)
        })
    }

    const options = [
        {
            label: '设置曲线',
            visible: true,
            feature: true,
            onClick: () => setOpen(true),
            line: true
        },
        {
            label: !isLocked ? '锁定' : '解锁',
            visible: openExperiment, // 试验中才能使用
            feature: true,
            onClick: () => setIsLocked(!isLocked)
        },
        {
            label: '恢复',
            visible: true,
            feature: true,
            onClick: () => onRestore?.(),
            line: true
        },
        {
            label: '拷贝到剪贴板',
            visible: !openExperiment,
            feature: true,
            onClick: () => {
                copyClipboard()
            }

        },
        {
            label: '打印曲线图',
            visible: !openExperiment,
            feature: true,
            onClick: () => {
                printCurve()
            }
        },
        {
            label: '导出csv',
            visible: !openExperiment,
            feature: true
        },
        {
            label: openCross ? '关闭十字线' : '激活十字线',
            visible: !openExperiment,
            // 不能操作：开启断裂点
            feature: !openBreak && !isMarking,
            onClick: () => setOpenCross(!openCross)
        },
        {
            label: openBreak ? '关闭设置断裂点' : '设置断裂点',
            visible: !openExperiment,
            feature: !openCross && !isMarking,
            onClick: () => setOpenBreak(!openBreak)
        },
        {
            label: '撤销断裂点',
            visible: !openExperiment,
            feature: !openCross && !isMarking,
            onClick: () => onClearBreakPoint?.()
        },
        {
            label: isMarking ? '停止手工标记' : '激活手工标记',
            visible: !openExperiment,
            feature: !openCross && !openBreak,
            onClick: isMarking ? onStopMarking : onActivateMarking
        },
        {
            label: showPointTag ? '隐藏标签' : '显示标签',
            visible: !openExperiment,
            feature: true,
            onClick: () => {
                // 同步更新配置中的pointTag的open属性
                setShowPointTag(!showPointTag)
            }
        },
        {
            label: showChunkTag ? '隐藏标签块' : '显示标签块',
            visible: !openExperiment,
            feature: true,
            line: true,
            onClick: () => {
                // 同步更新配置中的chunkTag的open属性
                setShowChunkTag(!showChunkTag)
            }
        }
    ].filter(i => !!i && i.visible)

    return (
        <RightMenu
            domId={domId}
            options={options}
            layoutConfig={layoutConfig}
            capture
        />
    )
}

export default ContextMenu
