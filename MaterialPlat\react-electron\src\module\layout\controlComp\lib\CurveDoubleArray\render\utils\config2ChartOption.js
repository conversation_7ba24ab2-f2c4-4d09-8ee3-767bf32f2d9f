import { INPUT_VARIABLE_TYPE } from '@/utils/constants'
import { getCurrentResultById, getCurrentResultVariableById } from '@/hooks/project/resultVariable/utils'

import {
    getUnitRatio,
    getProcessID, numberFormat, resultFractionalDigit, unitConversion
} from '@/utils/utils'

import { formatResultLable } from './formatResultLable'
import { getAuxiliaryOption } from './getAuxiliaryOption'

const getAxisOption = (id, axis) => {
    const {
        thickness,
        zeroLineColor,
        color,
        zeroLineThickness,
        isGrid,
        name,
        type,
        isLog,
        zeroLineType,
        isZeroLine,
        gridThickness,
        lastRange,
        lowLimit,
        proportionType,
        upLimit,
        gridColor,
        gridType
    } = axis

    return {
        id,
        title: name,
        style: {
            thickness,
            lineType: type,
            color
        },
        gridLine: {
            open: isGrid,
            thickness: gridThickness,
            color: gridColor,
            lineType: gridType
        },
        zeroLine: {
            open: isZeroLine,
            thickness: zeroLineThickness,
            color: zeroLineColor,
            lineType: zeroLineType
        },
        interval: {
            proportionType,
            start: lowLimit,
            end: upLimit,
            isLog,
            lastRange
        }
    }
}

const getLinesOption = ({ base, xAxis: xAxisOpion, curveGroup }) => {
    const lines = []

    const { xOffset = 0, yOffset = 0 } = base

    Object.entries(curveGroup).forEach(([yAxisId, item]) => {
        const {
            isEnable, name, xSignal, xUnit, curves
        } = item

        if (!isEnable) {
            return
        }

        let count = 0
        Object.entries(curves).forEach(([dataSourceKey, dataSrouce]) => {
            dataSrouce.lines.forEach(({
                id,
                name: lineName,
                color,
                signEach,
                isLine,
                signType,
                lineType,
                code,
                yUnit,
                xName,
                yName,
                lineThickness,
                isSign
            }, lineIndex) => {
                lines.push({
                    // 线的标识
                    id,
                    title: lineName,
                    xAxisId: '1',
                    yAxisId,
                    xSignal,
                    ySignal: code,
                    xRatio: getUnitRatio(xUnit),
                    yRatio: getUnitRatio(yUnit),
                    xOffset: count * xOffset,
                    yOffset: count * yOffset,
                    xName,
                    yName,
                    dataSourceKey,
                    style: {
                        isLine,
                        color,
                        thickness: lineThickness,
                        lineStyle: lineType,
                        isSign,
                        signStyle: signType,
                        signEach
                    }
                })

                count += 1
            })
        })
    })

    return lines
}

const getMarkerPointOption = ({ curveGroup, pointTagOpen, compStatus }) => {
    const markerPoint = []

    // 如果点标签未开启，直接返回空数组
    if (!pointTagOpen) {
        return markerPoint
    }

    // 遍历所有y轴组
    Object.entries(curveGroup).forEach(([yAxisId, axisGroup]) => {
        const { isEnable, curves } = axisGroup

        // 未启用不处理
        if (!isEnable) {
            return
        }

        // 遍历每个数据源数组
        Object.entries(curves).forEach(([doubleArrayIndex, dataSrouce]) => {
            // 遍历每条线
            dataSrouce.lines.forEach((lineConfig, lineIndex) => {
                const { pointTags, id } = lineConfig

                // 如果没有点标注配置，跳过
                if (!pointTags || pointTags.length === 0) {
                    return
                }

                // 生成线的ID
                const lineId = id

                // 为每个点标注生成标记点
                pointTags.forEach((pointTag, tagIndex) => {
                    const {
                        id: tagId,
                        color,
                        resultVariableId,
                        isName,
                        isChunk,
                        isLine,
                        isVal,
                        isAbbr
                    } = pointTag

                    const result = getCurrentResultById(resultVariableId)
                    const resultVariable = getCurrentResultVariableById(resultVariableId)

                    const res = formatResultLable({
                        result,
                        resultVariable,
                        isName,
                        isAbbr,
                        isVal
                    })

                    if (result && result.index !== undefined && result.index !== -1) {
                        markerPoint.push({
                            id: tagId,
                            lineId,
                            title: res,
                            isLine,
                            isChunk,
                            color,
                            pointIndex: result.index,
                            position: compStatus?.pointTag?.position?.[tagId]
                        })
                    }
                })
            })
        })
    })

    return markerPoint
}

const getMarkerChunkOption = ({ open, list }, compStatus) => {
    const markerChunk = []

    if (!open) {
        return markerChunk
    }

    list.forEach((chunkConfig) => {
        const {
            id,
            showTitle,
            title,
            color,
            isName,
            isChunk,
            isSample,
            isVal,
            isAbbr,
            curveIndex,
            results
        } = chunkConfig

        const content = []

        // 获取当前要显示的结果内容
        results.forEach((resultId) => {
            const result = getCurrentResultById(resultId)
            const resultVariable = getCurrentResultVariableById(resultId)
            const res = formatResultLable({
                result,
                resultVariable,
                isName,
                isAbbr,
                isVal
            })
            if (res) {
                content.push(res)
            }
        })

        markerChunk.push({
            id,
            color: color || '#000000',
            showBorder: false,
            showTitle,
            title,
            content,
            position: compStatus?.chunkTag?.position?.[id]
        })
    })

    return markerChunk
}

const getChartBaseOption = (option) => {
    const { isName, name } = option

    return {
        title: isName ? name : ''
    }
}

export const config2ChartOption = (config, compStatus) => {
    const {
        base, xAxis, yAxis, y2Axis, curveGroup, legend, chunkTag, pointTag
    } = config

    return {
        // 图表
        chart: getChartBaseOption(base),
        // x轴
        xAxis: [getAxisOption('1', xAxis)],
        // y轴
        yAxis: curveGroup.y2Axis.isEnable
            ? [getAxisOption('yAxis', yAxis), getAxisOption('y2Axis', y2Axis)]
            : [getAxisOption('yAxis', yAxis)],
        // 变化1：直接把线配置传进来
        lines: getLinesOption(config),
        legend: {
            open: legend?.open
        },
        // 变化2： 结果标注
        markerPoint: getMarkerPointOption({ curveGroup, pointTagOpen: pointTag?.open, compStatus }),
        markerChunk: getMarkerChunkOption(chunkTag, compStatus),
        auxiliary: getAuxiliaryOption(config)
    }
}
