/* eslint-disable no-param-reassign */
import {
    AxisTickStrategies,
    emptyTick,
    emptyFill,
    emptyLine,
    DashedLine,
    SolidLine,
    SolidFill,
    ColorCSS,
    UIBackgrounds,
    MarkerBuilders,
    UIOrigins,
    PointMarkers,
    UILayoutBuilders,
    UIElementBuilders,
    UIDraggingModes,
    UIVisibilityModes,
    AxisPosition,
    LegendBoxBuilders,
    ChartXYTitlePositionOptions,
    ImageFill,
    UIDirections,
    ImageFitMode,
    translatePoint,
    PointShape
} from '@arction/lcjs'

import { LINE_STYLE_TYPE, INTERVAL_TYPE } from '../constants/axis'
import { LINE_POINT_STYLE_TYPE } from '../constants/index'
import { PointTagManager } from './pointTag'
import { getAuxiliaryLineStyle } from './auxiliaryLines'
import { initSingleChunk } from './chunkMarker'

const getLineStyle = ({ lineType, thickness, color }) => {
    switch (lineType) {
    case LINE_STYLE_TYPE['------']:
        return new DashedLine({
            thickness,
            fillStyle: new SolidFill({ color: ColorCSS(color || '#000000') }),
            patternScale: 2
        })
    case LINE_STYLE_TYPE['......']:
        return new DashedLine({
            thickness,
            fillStyle: new SolidFill({ color: ColorCSS(color || '#000000') }),
            patternScale: 1
        })
    case LINE_STYLE_TYPE['——————']:
    default:
        return new SolidLine({
            thickness,
            fillStyle: new SolidFill({ color: ColorCSS(color || '#000000') })
        })
    }
}

const initAxis = (axis, axisOpion) => {
    const {
        id,
        title,
        style: { ...axisLineStyle },
        gridLine: {
            open: openGridLine, ...gridLineStyle
        },
        zeroLine: {
            open: openZeroLine, ...zeroLineStyle
        },
        interval: { start = 0, end = 10, isLog }
    } = axisOpion

    axis.id = id
    if (title) {
        axis.setTitle(title)
    }
    if (axisLineStyle) {
        axis.setStrokeStyle(getLineStyle(axisLineStyle))
    }

    if (!isLog) {
        axis.setInterval({ start, end })
    }

    // 零线
    if (openZeroLine) {
        axis.addConstantLine()
            .setMouseInteractions(false)
            .setValue(0)
            .setStrokeStyle(getLineStyle(zeroLineStyle))
    }

    // 网格线
    axis.setTickStrategy(
        AxisTickStrategies.Numeric,
        (numericTicks) => {
            const line = openGridLine ? getLineStyle(gridLineStyle) : emptyLine

            return numericTicks
                .setMinorTickStyle((tickStyle) => tickStyle.setGridStrokeStyle(line))
                .setMajorTickStyle((tickStyle) => tickStyle.setGridStrokeStyle(line))
        }
    )
}

const initChart = (chartXY, chartOption) => {
    const { title } = chartOption

    chartXY.setTitle(title)
}

const initXAxises = (chartXY, xAxisesOption) => {
    const xAxisMap = Object.fromEntries(
        xAxisesOption.map((axisOption) => {
            const { id, interval: { isLog } } = axisOption

            const axis = chartXY.addAxisX({
                type: isLog ? 'logarithmic' : 'linear-highPrecision',
                base: 10
            })

            initAxis(axis, axisOption)

            return [id, axis]
        })
    )

    return xAxisMap
}

const initYAxises = (chartXY, yAxisesOption) => {
    const yAxisMap = Object.fromEntries(
        yAxisesOption.map((axisOption, index) => {
            const { id, interval: { isLog } } = axisOption

            const axis = chartXY.addAxisY({
                type: isLog ? 'logarithmic' : 'linear-highPrecision',
                base: 10,
                opposite: index === 1
            })

            initAxis(axis, axisOption)

            return [id, axis]
        })
    )

    return yAxisMap
}

const getLinePointStyle = (pointStyle) => {
    switch (pointStyle) {
    case LINE_POINT_STYLE_TYPE.三角形:
        return PointShape.Triangle
    case LINE_POINT_STYLE_TYPE.方形:
        return PointShape.Square
    case LINE_POINT_STYLE_TYPE.圆形:
    default:
        return PointShape.Circle
    }
}

const initLines = ({
    chartXY, xAxisMap, yAxisMap, lines: linesOption
}) => {
    const linesMap = {}

    linesOption.forEach((lineOption) => {
        const {
            id,
            title,
            xAxisId,
            yAxisId,
            xRatio,
            yRatio,
            xOffset,
            yOffset,
            xName,
            yName,
            style: {
                isLine,
                color = '#000',
                thickness,
                lineStyle,
                isSign,
                signStyle,
                signEach
            }
        } = lineOption

        const xAxis = xAxisMap[xAxisId]
        const yAxis = yAxisMap[yAxisId]

        let line

        if (isSign) {
            line = chartXY
                .addPointLineSeries({
                    xAxis,
                    yAxis,
                    pointShape: getLinePointStyle(signStyle)
                })
                // 是否可以在添加点的同时设置点的大小 如果要绘制所有点时 不开启 否则默认开启 每隔十个点 传入size：0 走默认大小  否则size:1 绘制最小
                .setIndividualPointSizeEnabled(!signEach)
                // 点线有的api  设置点的填充
                .setPointFillStyle(new SolidFill({ color: ColorCSS(color || '#000') }))
        } else {
            line = chartXY.addLineSeries({
                xAxis,
                yAxis
            })
        }

        line
            .setName(title)
            .setStrokeStyle(
                // 是否绘制线
                isLine ? getLineStyle({ lineType: lineStyle, thickness, color }) : emptyLine
            )
            .setCursorEnabled(false)
            .setHighlight(false)
            .setHighlightOnHover(false)
            .setEffect(false)

        // line.add([
        //     { x: 1, y: Math.random() * 10, size: 1 },
        //     { x: 5, y: Math.random() * 10, size: 1 },
        //     { x: 9, y: Math.random() * 10, size: 1 }
        // ])
        line.id = id
        line.xAxisId = xAxisId
        line.yAxisId = yAxisId
        line.xRatio = xRatio
        line.yRatio = yRatio
        line.xOffset = xOffset
        line.yOffset = yOffset
        line.xName = xName
        line.yName = yName

        linesMap[id] = line
    })

    return linesMap
}

const initLegend = (chartXY, linesMap, legendOption) => {
    // 只有当open为true时才创建图例
    if (!legendOption?.open) {
        return null
    }

    // 创建LightningChart原生图例
    const legend = chartXY
        .addLegendBox()
        .setTitle('')
        .setOrigin(UIOrigins.RightTop)
        .setPosition({ x: 100, y: 100 })
        .setMargin(10)
        .setDraggingMode(UIDraggingModes.draggable)
        .setVisible(true)

    // 添加图例项，禁用点击显示隐藏线的功能
    Object.values(linesMap).forEach(line => {
        legend.add(line, { toggleVisibilityOnClick: false })
    })

    return legend
}

const initResultLabel = (chartXY) => {
    // 结果标签 - 容器
    const layout = chartXY.addUIElement(UILayoutBuilders.Column)
        .setOrigin(UIOrigins.RightTop)
        .setPosition({ x: 100, y: 100 })
        .setMargin(10)
        // 默认不显示
        .setVisible(false)
        // 开启拖动
        // .setDraggingMode(UIDraggingModes.notDraggable)

    // 标题
    const titleElement = layout
        .addElement(UIElementBuilders.TextBox)
        .setText('十字线')

    // 索引文本
    const pointIndexText = layout
        .addElement(UILayoutBuilders.Row)
        .addElement(UIElementBuilders.TextBox)

    // 点x信息的文本
    const pointXText = layout
        .addElement(UILayoutBuilders.Row)
        .addElement(UIElementBuilders.TextBox)

    // 点y信息的文本
    const pointYText = layout
        .addElement(UILayoutBuilders.Row)
        .addElement(UIElementBuilders.TextBox)

    return {
        layout,
        titleElement,
        pointIndexText,
        pointXText,
        pointYText
    }
}

const initLineCross = (linesMap) => {
    const crossMarker = MarkerBuilders.XY
        .setPointMarker(PointMarkers.UICircle)
        .setResultTableBackground(UIBackgrounds.Rectangle)
        .addStyler(marker => marker.setPointMarker(pointMarker => pointMarker.setSize({ x: 5, y: 5 })))

    const lineCrossMap = Object.fromEntries(
        Object.entries(linesMap).map(([lineId, line]) => {
            const lineCross = line.addMarker(crossMarker)
                .setResultTableVisibility(false)
                // 鼠标事件
                .setMouseInteractions(false)
                // 轴标记可见
                .setTickMarkerXVisibility(false)
                .setTickMarkerYVisibility(false)
                // 初始化默认不可见
                .setVisible(false)

            return [lineId, lineCross]
        })
    )

    return lineCrossMap
}

const initLineMarkerPoint = (markerPointOption, linesMap, chartXY, xAxisMap, yAxisMap, onPointMarkerPositionChangeRef) => {
    // 存储标注配置信息，用于后续更新位置
    const markerPointMap = {}

    // 创建统一的点标注管理器实例
    const pointTagManager = new PointTagManager(chartXY, onPointMarkerPositionChangeRef)

    try {
        markerPointOption?.forEach(({
            id, lineId, title, pointIndex, isLine = true, isChunk = true, color = '#000000', position
        }) => {
            // 获取对应线条实例
            const lineInstance = linesMap[lineId]
            if (!lineInstance) {
                console.warn('未找到线条实例:', lineId)
                return
            }

            // 存储标注配置信息
            markerPointMap[id] = {
                pointIndex,
                lineId,
                title,
                lineInstance,
                isLine,
                isChunk,
                color,
                position
            }
        })
    } catch (error) {
        console.log('initLineMarkerPoint error:', error)
    }

    return { markerPointMap, pointTagManager }
}

const initLineMarkerChunk = (markerChunk, chartXY, onChunkMarkerPositionChangeRef) => {
    const markerChunkMap = {}

    try {
        markerChunk?.forEach(({
            id, content, color = '#000000', showBorder = true, title, showTitle, position
        }) => {
            const chunkContainer = initSingleChunk({
                chartXY, id, showTitle, title, color, content, position, onChunkMarkerPositionChangeRef
            })

            markerChunkMap[id] = {
                id,
                chunkContainer,
                color,
                showBorder
            }
        })
    } catch (error) {
        console.log('initLineMarkerChunk error:', error)
    }

    return markerChunkMap
}

// 辅助线初始化函数
const initAuxiliaryLines = (chartXY, auxiliaryConfig, xAxisMap, yAxisMap) => {
    const auxiliaryLinesMap = {}

    if (!auxiliaryConfig || auxiliaryConfig.length === 0) {
        return auxiliaryLinesMap
    }
    // 注意：getAuxiliaryLineStyle 函数现在从 auxiliaryLines.js 工具文件中导入

    try {
        auxiliaryConfig.forEach((config) => {
            const {
                id, type, configType, data, style, xAxisId, yAxisId
            } = config

            // 创建线条系列
            const line = chartXY.addLineSeries({
                dataPattern: 'ProgressiveX',
                xAxis: xAxisMap[xAxisId],
                yAxis: yAxisMap[yAxisId]
            })

            line.setStrokeStyle(getAuxiliaryLineStyle(style))
            line.setCursorEnabled(false)
            line.setHighlightOnHover(false)
            line.setEffect(false)

            // 存储辅助线信息
            auxiliaryLinesMap[id] = {
                id,
                line,
                config,
                type,
                configType,
                data,
                style,
                xAxisId,
                yAxisId
            }
        })
    } catch (error) {
        console.log('initAuxiliaryLines error:', error)
    }

    return auxiliaryLinesMap
}

export const initChartOption = (
    chartXY, option, onPointMarkerPositionChangeRef, onChunkMarkerPositionChangeRef
) => {
    const {
        chart, xAxis, yAxis, lines, markerPoint, markerChunk, legend: legendOption, auxiliary
    } = option

    // 去掉默认的x轴 y轴
    chartXY.getDefaultAxisX().dispose()
    chartXY.getDefaultAxisY().dispose()

    let xAxisMap = {}
    let yAxisMap = {}
    let linesMap = {}
    let legend = {}
    let resultLabel = {}
    let lineCrossMap = {}
    let markerPointMap = {}
    let markerChunkMap = {}
    let auxiliaryLinesMap = {}

    // 图表配置
    initChart(chartXY, chart)
    // x轴配置
    if (xAxis) {
        xAxisMap = initXAxises(chartXY, xAxis)
    }

    // y轴配置
    if (yAxis) {
        yAxisMap = initYAxises(chartXY, yAxis)
    }

    // 线配置
    if (xAxisMap && yAxisMap && lines) {
        linesMap = initLines({
            chartXY, xAxisMap, yAxisMap, lines
        })
    }

    // 辅助线配置
    if (xAxisMap && yAxisMap && auxiliary) {
        auxiliaryLinesMap = initAuxiliaryLines(chartXY, auxiliary, xAxisMap, yAxisMap)
    }

    // 图例配置
    if (linesMap) {
        legend = initLegend(chartXY, linesMap, legendOption)
    }

    // 十字线 和 选中的结果标签
    if (linesMap) {
        resultLabel = initResultLabel(chartXY)
        lineCrossMap = initLineCross(linesMap)
    }

    // 点标注
    let pointTagManager = null
    if (linesMap && markerPoint) {
        const result = initLineMarkerPoint(markerPoint, linesMap, chartXY, xAxisMap, yAxisMap, onPointMarkerPositionChangeRef)
        markerPointMap = result.markerPointMap
        pointTagManager = result.pointTagManager
    }

    // 块标注
    if (linesMap && markerChunkMap) {
        markerChunkMap = initLineMarkerChunk(markerChunk, chartXY, onChunkMarkerPositionChangeRef)
    }

    return {
        xAxisMap,
        yAxisMap,
        linesMap,
        legend,
        resultLabel,
        lineCrossMap,
        markerPointMap,
        markerChunkMap,
        pointTagManager,
        auxiliaryLinesMap
    }
}
