/* eslint-disable no-param-reassign */
import {
    ColorCSS,
    ColorRGBA,
    SolidFill,
    SolidLine,
    UIElementBuilders,
    UIDraggingModes
} from '@arction/lcjs'

/**
 * 点标注管理器
 * 用于创建、管理和控制图表上的点标注
 */
export class PointTagManager {
    constructor(chart, onPointMarkerPositionChangeRef) {
        this.chart = chart
        this.annotations = new Map() // 存储所有标注，key为id
        this.visible = true // 全局可见性状态
        this.onPointMarkerPositionChangeRef = onPointMarkerPositionChangeRef || null // 位置变化回调函数
    }

    /**
   * 创建单个标注
   * @param {Object} config - 标注配置
   * @param {string} config.id - 标注唯一ID
   * @param {number} config.x - X坐标
   * @param {number} config.y - Y坐标
   * @param {string} config.content - 标注内容
   * @param {Object} config.lineInstance - 线实例，用于获取轴信息
   * @param {Object} config.style - 样式配置（可选）
   * @param {Object} config.position - 标注位置配置（可选）
   * @param {number} config.position.x - 自定义标注X位置
   * @param {number} config.position.y - 自定义标注Y位置
   */
    createAnnotation(config) {
        const {
            id, x, y, content, lineInstance, isLine = true, isChunk = true, color = '#000000', style = {}, position = null
        } = config

        if (!lineInstance) {
            console.error('创建标注时必须提供 lineInstance 参数')
            return null
        }

        // 从线实例获取轴信息
        const xAxis = lineInstance.axisX
        const yAxis = lineInstance.axisY

        if (this.annotations.has(id)) {
            console.warn(`标注ID "${id}" 已存在，将覆盖原有标注`)
            this.removeAnnotation(id)
        }

        // 计算标注框的位置
        // 如果提供了自定义位置，使用自定义位置；否则使用默认位置（在数据点上方）
        const annotationX = position ? position.x : x
        const annotationY = position ? position.y : (y + (style.offsetY || 1)) // 默认在数据点上方1个单位

        // 计算标注框中心位置
        const annotationCenterY = annotationY

        // 根据isLine属性决定是否创建连接线
        let connectionLine = null
        if (isLine) {
            // 创建连接线数据（从数据点到标注框中心）
            const connectionData = [
                { x, y },
                { x: annotationX, y: annotationCenterY }
            ]

            // 创建连接线
            connectionLine = this.chart.addLineSeries({
                dataPattern: 'ProgressiveX'
            })
                .setName(`连接线-${id}`)
                .setStrokeStyle(new SolidLine({
                    thickness: style.lineThickness || 1,
                    fillStyle: new SolidFill({
                        color: style.lineColor || ColorRGBA(128, 128, 128)
                    })
                }))
                .add(connectionData)
                .setCursorEnabled(false)
                .setHighlightOnHover(false)
                .setEffect(false)
        }

        // 创建标注矩形
        const annotationBox = this.chart.addUIElement(UIElementBuilders.TextBox, {
            x: xAxis,
            y: yAxis
        })
            .setText(content)
            .setTextFillStyle(new SolidFill({ color: ColorCSS(color) }))
            .setPosition({ x: annotationX, y: annotationY })
            .setOrigin({ x: 0, y: 0 })
            .setMargin({
                left: style.marginLeft || 8,
                right: style.marginRight || 8,
                top: style.marginTop || 4,
                bottom: style.marginBottom || 4
            })
            .setDraggingMode(UIDraggingModes.draggable)

        // 根据isChunk属性设置边框
        if (!isChunk) {
            // 移除边框：设置透明边框
            annotationBox.setBackground(background => background
                .setStrokeStyle(new SolidLine({
                    thickness: 0,
                    fillStyle: new SolidFill({ color: ColorRGBA(0, 0, 0, 0) })
                })))
        }

        // 添加鼠标拖拽事件监听
        const dragStartToken = annotationBox.onMouseDragStart((event) => {
            if (connectionLine) {
                connectionLine.setVisible(false)
            }
        })

        const dragStopToken = annotationBox.onMouseDragStop((event) => {
            const currentPosition = annotationBox.getPosition()

            if (connectionLine) {
                const currentCenterY = currentPosition.y

                // 获取当前标注的最新原始坐标
                const annotation = this.annotations.get(id)
                const originalX = annotation ? annotation.originalX : x
                const originalY = annotation ? annotation.originalY : y

                const newConnectionData = [
                    { x: originalX, y: originalY }, // 使用最新的原始数据点坐标
                    { x: currentPosition.x, y: currentCenterY }
                ]

                connectionLine.clear().add(newConnectionData)
                connectionLine.setVisible(true)
            }

            // 触发位置变化回调
            if (this.onPointMarkerPositionChangeRef) {
                this.onPointMarkerPositionChangeRef.current({
                    id,
                    position: {
                        x: currentPosition.x,
                        y: currentPosition.y
                    },
                    originalPosition: {
                        x,
                        y
                    }
                })
            }
        })

        // 存储标注信息
        const annotation = {
            id,
            originalX: x,
            originalY: y,
            annotationX,
            annotationY,
            connectionLine,
            annotationBox,
            dragStartToken,
            dragStopToken,
            visible: true
        }

        this.annotations.set(id, annotation)

        // 如果全局不可见，隐藏新创建的标注
        if (!this.visible) {
            this.hideAnnotation(id)
        }

        return annotation
    }

    /**
   * 移除指定标注
   * @param {string} id - 标注ID
   */
    removeAnnotation(id) {
        const annotation = this.annotations.get(id)
        if (!annotation) return false

        // 清理事件监听器
        // if (annotation.dragStartToken) {
        //     annotation.dragStartToken()
        // }
        // if (annotation.dragStopToken) {
        //     annotation.dragStopToken()
        // }

        // 清理图表元素
        if (annotation.connectionLine) {
            annotation.connectionLine.dispose()
        }
        if (annotation.annotationBox) {
            annotation.annotationBox.dispose()
        }

        this.annotations.delete(id)
        return true
    }

    /**
     * 获取指定标注的当前位置信息
     * @param {string} id - 标注ID
     * @returns {Object|null} 位置信息对象，包含当前位置和原始数据点位置
     */
    getAnnotationPosition(id) {
        const annotation = this.annotations.get(id)
        if (!annotation) return null

        const currentPosition = annotation.annotationBox.getPosition()
        return {
            id,
            currentPosition: {
                x: currentPosition.x,
                y: currentPosition.y
            },
            originalPosition: {
                x: annotation.originalX,
                y: annotation.originalY
            }
        }
    }

    /**
     * 获取所有标注的当前位置信息
     * @returns {Array} 所有标注的位置信息数组
     */
    getAllAnnotationPositions() {
        const positions = []
        this.annotations.forEach((annotation, id) => {
            const positionInfo = this.getAnnotationPosition(id)
            if (positionInfo) {
                positions.push(positionInfo)
            }
        })
        return positions
    }

    /**
     * 更新指定标注的位置和内容
     * @param {string} id - 标注ID
     * @param {number} pointIndex - 数据点下标
     * @param {Array} lineData - 线条数据数组
     * @param {string} [content] - 可选的新标注内容
     * @param {Object} [position] - 可选的标注位置配置
     * @param {number} [position.x] - 自定义标注X位置
     * @param {number} [position.y] - 自定义标注Y位置
     */
    updateAnnotationPosition(id, pointIndex, lineData, content, position = null) {
        const annotation = this.annotations.get(id)
        if (!annotation) return false

        if (!lineData || pointIndex >= lineData.length || pointIndex < 0) {
            console.warn(`无效的数据点下标: ${pointIndex}`)
            return false
        }

        // 从数据数组中获取新的坐标
        const dataPoint = lineData[pointIndex]
        const { x } = dataPoint
        const { y } = dataPoint

        // 更新原始坐标
        annotation.originalX = x
        annotation.originalY = y

        // 如果提供了新内容，更新标注文本
        if (content !== undefined && content !== null) {
            annotation.annotationBox.setText(content)
            annotation.title = content // 同时更新内部存储的标题
        }

        // 计算新的标注框位置
        // 如果提供了自定义位置，使用自定义位置；否则使用默认位置（在数据点上方）
        const annotationX = position ? position.x : x
        const annotationY = position ? position.y : (y + 1)
        const annotationCenterY = annotationY

        // 更新标注框位置
        annotation.annotationBox.setPosition({ x: annotationX, y: annotationY })

        // 更新连接线（如果存在）
        if (annotation.connectionLine) {
            const newConnectionData = [
                { x, y },
                { x: annotationX, y: annotationCenterY }
            ]
            annotation.connectionLine.clear().add(newConnectionData)
        }

        return true
    }

    /**
   * 显示指定标注
   * @param {string} id - 标注ID
   */
    showAnnotation(id) {
        const annotation = this.annotations.get(id)
        if (!annotation) return false

        if (annotation.connectionLine) {
            annotation.connectionLine.setVisible(true)
        }
        annotation.annotationBox.setVisible(true)
        annotation.visible = true
        return true
    }

    /**
   * 隐藏指定标注
   * @param {string} id - 标注ID
   */
    hideAnnotation(id) {
        const annotation = this.annotations.get(id)
        if (!annotation) return false

        if (annotation.connectionLine) {
            annotation.connectionLine.setVisible(false)
        }
        annotation.annotationBox.setVisible(false)
        annotation.visible = false
        return true
    }

    /**
   * 显示所有标注
   */
    showAllAnnotations() {
        this.visible = true
        this.annotations.forEach((annotation) => {
            if (annotation.connectionLine) {
                annotation.connectionLine.setVisible(true)
            }
            annotation.annotationBox.setVisible(true)
            annotation.visible = true
        })
    }

    /**
   * 隐藏所有标注
   */
    hideAllAnnotations() {
        this.visible = false
        this.annotations.forEach((annotation) => {
            if (annotation.connectionLine) {
                annotation.connectionLine.setVisible(false)
            }
            annotation.annotationBox.setVisible(false)
            annotation.visible = false
        })
    }

    /**
   * 切换所有标注的可见性
   */
    toggleAllAnnotations() {
        if (this.visible) {
            this.hideAllAnnotations()
        } else {
            this.showAllAnnotations()
        }
    }

    /**
   * 获取指定标注信息
   * @param {string} id - 标注ID
   */
    getAnnotation(id) {
        return this.annotations.get(id)
    }

    /**
   * 获取所有标注信息
   */
    getAllAnnotations() {
        return Array.from(this.annotations.values())
    }

    /**
   * 清理所有标注
   */
    dispose() {
        this.annotations.forEach((annotation) => {
            // // 清理事件监听器
            // if (annotation.dragStartToken) {
            //     annotation.dragStartToken()
            // }
            // if (annotation.dragStopToken) {
            //     annotation.dragStopToken()
            // }

            // 清理图表元素
            if (annotation.connectionLine) {
                annotation.connectionLine.dispose()
            }
            if (annotation.annotationBox) {
                annotation.annotationBox.dispose()
            }
        })

        this.annotations.clear()
    }

    /**
   * 获取标注数量
   */
    getAnnotationCount() {
        return this.annotations.size
    }

    /**
   * 检查标注是否存在
   * @param {string} id - 标注ID
   */
    hasAnnotation(id) {
        return this.annotations.has(id)
    }
}
