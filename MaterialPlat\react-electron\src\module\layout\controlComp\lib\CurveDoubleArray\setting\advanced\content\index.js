import React from 'react'

import StepBase from './stepBase'
import StepCurve from './stepCurve'
import StepAxisX from './stepAxisX'
import StepAxisY from './stepAxisY'
import StepAxisY2 from './stepAxisY2'
import StepAuxiliary from './stepAuxiliary'
import StepMarker from './stepMarker'
import StepTag from './stepTag'
import StepDefineAxis from './stepDefineAxis'
import { steps } from '../../constants'

const stepContentMap = {
    曲线图: StepBase,
    曲线组: StepCurve,
    'X-轴': StepAxisX,
    'Y-轴1': StepAxisY,
    'Y-轴2': StepAxisY2,
    辅助线: StepAuxiliary,
    标签: StepTag,
    标记点设置: StepMarker,
    定义坐标源: StepDefineAxis
}

const Content = ({ channels, currentStep, isBufferCurve }) => {
    return steps.map(s => {
        const Comp = stepContentMap[s]
        return (
            <div
                style={{ display: currentStep === s ? 'block' : 'none' }}
            >
                <Comp channels={channels} isBufferCurve={isBufferCurve} />
            </div>
        )
    })
}

export default Content
