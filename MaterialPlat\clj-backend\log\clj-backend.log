25-08-26 09:05:27:495 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "6640a1f9-5901-41bf-a519-52c3416388a9", :HostName "1", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-08-26 09:05:27:758 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "56813bcb-0918-4d11-86a9-98746da15518"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-08-26 09:05:27:878 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-08-26 09:05:27:945 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-08-26 09:05:28:407 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-08-26 09:05:28:410 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-08-26 09:05:48:804 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_2 应用性能优化配置
25-08-26 09:05:48:805 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_2 项目库连接
25-08-26 09:05:49:062 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_2"}
25-08-26 09:05:49:089 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-08-26 09:05:53:133 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "b5375b43-5d5b-4669-8702-c90d003b0d9e", :code 0, :msg "模板生成成功"}
25-08-26 09:05:53:135 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-b5375b43-5d5b-4669-8702-c90d003b0d9e 中添加消息 {:ProcessId "b5375b43-5d5b-4669-8702-c90d003b0d9e", :code 0, :msg "模板生成成功"}
25-08-26 09:05:53:139 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "b5375b43-5d5b-4669-8702-c90d003b0d9e", :code 0, :msg "模板生成成功"}
25-08-26 09:05:54:038 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_408c390a", :SelectedInstCodes []} 
 =============================================================

25-08-26 09:05:54:504 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【默认执行】"}
25-08-26 09:05:54:519 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-78639282-fa6a-4822-b502-d2144342e71a", :State "running"} 
 =============================================================

25-08-26 09:05:55:613 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_2", :ScriptId "b284fe96-b2fe-4ab5-a15c-6723e4821c10", :Result false}
25-08-26 09:05:55:615 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-b284fe96-b2fe-4ab5-a15c-6723e4821c10 中添加消息 {:ProcessId "project_2", :ScriptId "b284fe96-b2fe-4ab5-a15c-6723e4821c10", :Result false}
25-08-26 09:05:55:617 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_2", :ScriptId "b284fe96-b2fe-4ab5-a15c-6723e4821c10", :Result false}
25-08-26 09:05:55:650 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_2", :ScriptId "d857535a-2aa4-4bd2-acb1-2716317f4681", :Result false}
25-08-26 09:05:55:651 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-d857535a-2aa4-4bd2-acb1-2716317f4681 中添加消息 {:ProcessId "project_2", :ScriptId "d857535a-2aa4-4bd2-acb1-2716317f4681", :Result false}
25-08-26 09:05:55:653 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_2", :ScriptId "d857535a-2aa4-4bd2-acb1-2716317f4681", :Result false}
25-08-26 09:05:55:729 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 09:05:55:733 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【更新试验信息】"}
25-08-26 09:05:55:735 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "running"} 
 =============================================================

25-08-26 09:05:55:852 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 09:05:55:856 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "finished"} 
 =============================================================

25-08-26 09:05:55:928 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-08-26 09:05:55:934 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【联机DAQ】"}
25-08-26 09:05:55:940 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "running"} 
 =============================================================

25-08-26 09:05:56:053 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【默认执行】"}
25-08-26 09:05:56:055 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-78639282-fa6a-4822-b502-d2144342e71a", :State "finished"} 
 =============================================================

25-08-26 09:07:58:123 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_2】项目"}
25-08-26 09:07:58:136 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【默认执行】"}
25-08-26 09:07:58:146 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 09:07:58:156 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【联机DAQ】"}
25-08-26 09:07:58:157 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "finished"} 
 =============================================================

25-08-26 09:07:59:903 DESKTOP-3BSREDP WARN [clj-backend.db.connections:113] - 销毁 project_2 数据库连接
25-08-26 09:07:59:907 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756115767142_project_2.db 成功
25-08-26 09:07:59:980 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_2 应用性能优化配置
25-08-26 09:07:59:982 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_2 项目库连接
25-08-26 09:08:32:693 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:643] - Project event listener stopping.
25-08-26 09:08:32:709 DESKTOP-3BSREDP INFO [clj-backend.db.core:31] - 已为系统数据库应用 SQLite 性能优化配置
25-08-26 09:08:32:712 DESKTOP-3BSREDP INFO [clj-backend.modules.inspection.inspection-record-routes:105] - 开启生成点检记录定时任务,每天1点执行!
25-08-26 09:08:32:712 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-08-26 09:08:32:713 DESKTOP-3BSREDP INFO [clj-backend.modules.inspection.inspection-record-routes:121] - 开启每日点检提醒,每天1点30分提醒
25-08-26 09:08:32:715 DESKTOP-3BSREDP INFO [clj-backend.modules.inspection.inspection-record-service:191] - 新生成点检记录:0 条
25-08-26 09:08:32:719 DESKTOP-3BSREDP INFO [clj-backend.modules.inspection.inspection-record-service:236] - 今天有0项设备未进行点检！
25-08-26 09:08:32:721 DESKTOP-3BSREDP INFO [clj-backend.modules.inspection.inspection-record-service:237] - 近期有0项设备未进行点检！
25-08-26 09:13:29:747 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "6640a1f9-5901-41bf-a519-52c3416388a9", :HostName "1", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-08-26 09:13:29:996 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "56813bcb-0918-4d11-86a9-98746da15518"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-08-26 09:13:30:088 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-08-26 09:13:30:130 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-08-26 09:13:30:575 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-08-26 09:13:30:577 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-08-26 09:16:58:089 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_2 应用性能优化配置
25-08-26 09:16:58:090 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_2 项目库连接
25-08-26 09:16:58:240 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_2"}
25-08-26 09:16:58:252 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-08-26 09:17:01:782 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "b8040209-16aa-41e7-832e-9c191d483a5a", :code 0, :msg "模板生成成功"}
25-08-26 09:17:01:784 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-b8040209-16aa-41e7-832e-9c191d483a5a 中添加消息 {:ProcessId "b8040209-16aa-41e7-832e-9c191d483a5a", :code 0, :msg "模板生成成功"}
25-08-26 09:17:01:785 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "b8040209-16aa-41e7-832e-9c191d483a5a", :code 0, :msg "模板生成成功"}
25-08-26 09:17:02:587 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_408c390a", :SelectedInstCodes []} 
 =============================================================

25-08-26 09:17:03:001 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【默认执行】"}
25-08-26 09:17:03:004 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-78639282-fa6a-4822-b502-d2144342e71a", :State "running"} 
 =============================================================

25-08-26 09:17:03:965 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_2", :ScriptId "002cba95-8844-4abc-a52d-dd6bd004af70", :Result false}
25-08-26 09:17:03:966 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-002cba95-8844-4abc-a52d-dd6bd004af70 中添加消息 {:ProcessId "project_2", :ScriptId "002cba95-8844-4abc-a52d-dd6bd004af70", :Result false}
25-08-26 09:17:03:967 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_2", :ScriptId "002cba95-8844-4abc-a52d-dd6bd004af70", :Result false}
25-08-26 09:17:04:069 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 09:17:04:073 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【更新试验信息】"}
25-08-26 09:17:04:075 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "running"} 
 =============================================================

25-08-26 09:17:04:247 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 09:17:04:248 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "finished"} 
 =============================================================

25-08-26 09:17:04:345 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-08-26 09:17:04:351 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【联机DAQ】"}
25-08-26 09:17:04:354 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "running"} 
 =============================================================

25-08-26 09:17:04:461 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【默认执行】"}
25-08-26 09:17:04:464 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-78639282-fa6a-4822-b502-d2144342e71a", :State "finished"} 
 =============================================================

25-08-26 09:51:41:106 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_2】项目"}
25-08-26 09:51:41:112 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【默认执行】"}
25-08-26 09:51:41:118 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 09:51:41:124 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【联机DAQ】"}
25-08-26 09:51:41:125 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "finished"} 
 =============================================================

25-08-26 09:51:42:876 DESKTOP-3BSREDP WARN [clj-backend.db.connections:113] - 销毁 project_2 数据库连接
25-08-26 09:51:42:879 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756115767142_project_2.db 成功
25-08-26 09:51:42:931 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_2 应用性能优化配置
25-08-26 09:51:42:931 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_2 项目库连接
25-08-26 10:07:11:443 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_2"}
25-08-26 10:07:11:451 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-08-26 10:07:11:744 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "2a1c7def-2959-4847-869e-b1649a05e41e", :code 0, :msg "模板生成成功"}
25-08-26 10:07:11:746 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-2a1c7def-2959-4847-869e-b1649a05e41e 中添加消息 {:ProcessId "2a1c7def-2959-4847-869e-b1649a05e41e", :code 0, :msg "模板生成成功"}
25-08-26 10:07:11:746 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "2a1c7def-2959-4847-869e-b1649a05e41e", :code 0, :msg "模板生成成功"}
25-08-26 10:07:12:574 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【默认执行】"}
25-08-26 10:07:12:577 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-78639282-fa6a-4822-b502-d2144342e71a", :State "running"} 
 =============================================================

25-08-26 10:07:12:768 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_2", :ScriptId "c360cf1e-1088-4667-b5aa-73a5fe0e78a0", :Result false}
25-08-26 10:07:12:769 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-c360cf1e-1088-4667-b5aa-73a5fe0e78a0 中添加消息 {:ProcessId "project_2", :ScriptId "c360cf1e-1088-4667-b5aa-73a5fe0e78a0", :Result false}
25-08-26 10:07:12:770 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_2", :ScriptId "c360cf1e-1088-4667-b5aa-73a5fe0e78a0", :Result false}
25-08-26 10:07:12:821 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:07:12:826 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【更新试验信息】"}
25-08-26 10:07:12:828 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "running"} 
 =============================================================

25-08-26 10:07:12:972 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 10:07:12:973 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "finished"} 
 =============================================================

25-08-26 10:07:13:084 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-08-26 10:07:13:152 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【联机DAQ】"}
25-08-26 10:07:13:154 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "running"} 
 =============================================================

25-08-26 10:07:13:276 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【默认执行】"}
25-08-26 10:07:13:278 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-78639282-fa6a-4822-b502-d2144342e71a", :State "finished"} 
 =============================================================

25-08-26 10:07:24:752 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_3954c162", :SelectedInstCodes []} 
 =============================================================

25-08-26 10:07:25:332 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【切换试样】"}
25-08-26 10:07:25:333 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :State "running"} 
 =============================================================

25-08-26 10:07:25:492 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :SubTaskID "onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2", :MsgBody {:Cmd "start", :InstCode "sample_3954c162", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:07:25:505 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【更新试验信息】"}
25-08-26 10:07:25:508 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "running"} 
 =============================================================

25-08-26 10:07:25:577 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【切换试样】"}
25-08-26 10:07:25:580 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :State "finished"} 
 =============================================================

25-08-26 10:07:25:627 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 10:07:25:629 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "finished"} 
 =============================================================

25-08-26 10:07:33:321 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "【超级管理员】admin: 更新了【平均负荷】输入变量"}
25-08-26 10:07:33:328 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/input-var 
 - 参数: 
 {:ClassName "project_2", :InputVar {:Unit "unit_cdw13c2640", :Dimension "dimension_Force", :Max -1, :hwKey "", :Mode nil, :FxVariableCode nil, :ActionId nil, :Min -1, :ContactInputCode "", :Code "input_avgload", :Value 7, :IsConstant false, :Type "Number", :IsOverall false, :SubTaskID nil, :IsCheck false}} 
 =============================================================

25-08-26 10:07:33:711 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_2", :ScriptId "e4ca82c0-e3f1-47c6-802b-83ebbb690c9d", :Result false}
25-08-26 10:07:33:712 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-e4ca82c0-e3f1-47c6-802b-83ebbb690c9d 中添加消息 {:ProcessId "project_2", :ScriptId "e4ca82c0-e3f1-47c6-802b-83ebbb690c9d", :Result false}
25-08-26 10:07:33:713 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_2", :ScriptId "e4ca82c0-e3f1-47c6-802b-83ebbb690c9d", :Result false}
25-08-26 10:07:40:188 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "【超级管理员】admin: 更新了【平均负荷】输入变量"}
25-08-26 10:07:40:193 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/input-var 
 - 参数: 
 {:ClassName "project_2", :InputVar {:Unit "unit_cdw13c2640", :Dimension "dimension_Force", :Max -1, :hwKey "", :Mode nil, :FxVariableCode nil, :ActionId nil, :Min -1, :ContactInputCode "", :Code "input_avgload", :Value 7, :IsConstant false, :Type "Number", :IsOverall false, :SubTaskID nil, :IsCheck false}} 
 =============================================================

25-08-26 10:07:40:268 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_2", :ScriptId "050cb09f-07de-42cc-a85a-0ef8a6c42972", :Result false}
25-08-26 10:07:40:268 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-050cb09f-07de-42cc-a85a-0ef8a6c42972 中添加消息 {:ProcessId "project_2", :ScriptId "050cb09f-07de-42cc-a85a-0ef8a6c42972", :Result false}
25-08-26 10:07:40:269 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_2", :ScriptId "050cb09f-07de-42cc-a85a-0ef8a6c42972", :Result false}
25-08-26 10:07:42:219 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_2】项目"}
25-08-26 10:07:42:224 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【默认执行】"}
25-08-26 10:07:42:229 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 10:07:42:235 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【联机DAQ】"}
25-08-26 10:07:42:236 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "finished"} 
 =============================================================

25-08-26 10:07:42:285 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【切换试样】"}
25-08-26 10:07:43:850 DESKTOP-3BSREDP WARN [clj-backend.db.connections:113] - 销毁 project_2 数据库连接
25-08-26 10:07:43:853 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756115767142_project_2.db 成功
25-08-26 10:07:43:899 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_2 应用性能优化配置
25-08-26 10:07:43:899 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_2 项目库连接
25-08-26 10:08:25:094 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "6640a1f9-5901-41bf-a519-52c3416388a9", :HostName "1", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-08-26 10:08:25:112 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:level "error", :content "调用外部接口出错"}
25-08-26 10:08:25:114 DESKTOP-3BSREDP ERROR [clj-backend.common.biz-error:48] - 发生系统错误. 
错误码: 10001 
错误描述: 调用外部接口出错 
错误数据: #error {
 :cause "Connection refused: connect"
 :via
 [{:type java.net.ConnectException
   :message "Connection refused: connect"
   :at [sun.nio.ch.Net connect0 "Net.java" -2]}]
 :trace
 [[sun.nio.ch.Net connect0 "Net.java" -2]
  [sun.nio.ch.Net connect "Net.java" 579]
  [sun.nio.ch.Net connect "Net.java" 568]
  [sun.nio.ch.NioSocketImpl connect "NioSocketImpl.java" 593]
  [java.net.SocksSocketImpl connect "SocksSocketImpl.java" 327]
  [java.net.Socket connect "Socket.java" 633]
  [org.apache.http.conn.scheme.PlainSocketFactory connectSocket "PlainSocketFactory.java" 120]
  [org.apache.http.impl.conn.DefaultClientConnectionOperator openConnection "DefaultClientConnectionOperator.java" 179]
  [org.apache.http.impl.conn.ManagedClientConnectionImpl open "ManagedClientConnectionImpl.java" 328]
  [org.apache.http.impl.client.DefaultRequestDirector tryConnect "DefaultRequestDirector.java" 612]
  [org.apache.http.impl.client.DefaultRequestDirector execute "DefaultRequestDirector.java" 447]
  [org.apache.http.impl.client.AbstractHttpClient doExecute "AbstractHttpClient.java" 884]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 82]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 107]
  [clj_http.core$request invokeStatic "core.clj" 304]
  [clj_http.core$request invoke "core.clj" 208]
  [clojure.lang.Var invoke "Var.java" 386]
  [clj_http.client$wrap_request_timing$fn__23262 invoke "client.clj" 835]
  [clj_http.headers$wrap_header_map$fn__22294 invoke "headers.clj" 143]
  [clj_http.client$wrap_query_params$fn__23159 invoke "client.clj" 661]
  [clj_http.client$wrap_basic_auth$fn__23166 invoke "client.clj" 677]
  [clj_http.client$wrap_oauth$fn__23170 invoke "client.clj" 687]
  [clj_http.client$wrap_user_info$fn__23175 invoke "client.clj" 700]
  [clj_http.client$wrap_url$fn__23248 invoke "client.clj" 801]
  [clj_http.client$wrap_redirects$fn__22935 invoke "client.clj" 267]
  [clj_http.client$wrap_decompression$fn__22960 invoke "client.clj" 339]
  [clj_http.client$wrap_input_coercion$fn__23092 invoke "client.clj" 490]
  [clj_http.client$wrap_additional_header_parsing$fn__23113 invoke "client.clj" 552]
  [clj_http.client$wrap_output_coercion$fn__23083 invoke "client.clj" 468]
  [clj_http.client$wrap_exceptions$fn__22921 invoke "client.clj" 219]
  [clj_http.client$wrap_accept$fn__23127 invoke "client.clj" 592]
  [clj_http.client$wrap_accept_encoding$fn__23133 invoke "client.clj" 609]
  [clj_http.client$wrap_content_type$fn__23122 invoke "client.clj" 584]
  [clj_http.client$wrap_form_params$fn__23222 invoke "client.clj" 761]
  [clj_http.client$wrap_nested_params$fn__23243 invoke "client.clj" 794]
  [clj_http.client$wrap_method$fn__23182 invoke "client.clj" 707]
  [clj_http.cookies$wrap_cookies$fn__21216 invoke "cookies.clj" 124]
  [clj_http.links$wrap_links$fn__22557 invoke "links.clj" 51]
  [clj_http.client$wrap_unknown_host$fn__23252 invoke "client.clj" 810]
  [clj_http.client$post invokeStatic "client.clj" 925]
  [clj_http.client$post doInvoke "client.clj" 921]
  [clojure.lang.RestFn invoke "RestFn.java" 426]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 29]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 25]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.modules.hardware.station.service$host_instance_handle invokeStatic "service.clj" 536]
  [clj_backend.modules.hardware.station.service$host_instance_handle invoke "service.clj" 522]
  [clj_backend.modules.sys.user.sys_user_service$login invokeStatic "sys_user_service.clj" 96]
  [clj_backend.modules.sys.user.sys_user_service$login invoke "sys_user_service.clj" 91]
  [clj_backend.modules.sys.user.sys_user_routes$routes$fn__43153 invoke "sys_user_routes.clj" 56]
  [clj_backend.common.trial$trial_middleware$fn__42702 invoke "trial.clj" 73]
  [reitit.ring.coercion$fn__52556$fn__52558$fn__52559 invoke "coercion.cljc" 40]
  [reitit.ring.coercion$fn__52579$fn__52581$fn__52582 invoke "coercion.cljc" 80]
  [reitit.ring.middleware.exception$wrap$fn__49535$fn__49536 invoke "exception.clj" 49]
  [clj_backend.middleware.logger$logger_middleware$fn__52774 invoke "logger.clj" 18]
  [muuntaja.middleware$wrap_format_request$fn__52697 invoke "middleware.clj" 114]
  [muuntaja.middleware$wrap_format_response$fn__52701 invoke "middleware.clj" 132]
  [muuntaja.middleware$wrap_format_negotiate$fn__52694 invoke "middleware.clj" 96]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [reitit.ring$ring_handler$fn__12807 invoke "ring.cljc" 329]
  [clojure.lang.AFn applyToHelper "AFn.java" 154]
  [clojure.lang.AFn applyTo "AFn.java" 144]
  [clojure.lang.AFunction$1 doInvoke "AFunction.java" 33]
  [clojure.lang.RestFn invoke "RestFn.java" 411]
  [clojure.lang.Var invoke "Var.java" 386]
  [ring.middleware.reload$wrap_reload$fn__16585 invoke "reload.clj" 39]
  [selmer.middleware$wrap_error_page$fn__16600 invoke "middleware.clj" 18]
  [prone.middleware$wrap_exceptions$fn__16842 invoke "middleware.clj" 169]
  [ring.middleware.flash$wrap_flash$fn__16963 invoke "flash.clj" 39]
  [ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290 invoke "session.clj" 88]
  [ring.middleware.cors$handle_cors invokeStatic "cors.cljc" 175]
  [ring.middleware.cors$handle_cors invoke "cors.cljc" 167]
  [ring.middleware.cors$wrap_cors$fn__16942 invoke "cors.cljc" 205]
  [ring.middleware.keyword_params$wrap_keyword_params$fn__18384 invoke "keyword_params.clj" 53]
  [ring.middleware.nested_params$wrap_nested_params$fn__18442 invoke "nested_params.clj" 89]
  [ring.middleware.multipart_params$wrap_multipart_params$fn__18740 invoke "multipart_params.clj" 173]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [ring.middleware.cookies$wrap_cookies$fn__18059 invoke "cookies.clj" 175]
  [ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935 invoke "absolute_redirects.clj" 47]
  [ring.middleware.resource$wrap_resource_prefer_resources$fn__18800 invoke "resource.clj" 25]
  [ring.middleware.content_type$wrap_content_type$fn__18883 invoke "content_type.clj" 34]
  [ring.middleware.default_charset$wrap_default_charset$fn__18907 invoke "default_charset.clj" 31]
  [ring.middleware.not_modified$wrap_not_modified$fn__18864 invoke "not_modified.clj" 61]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.adapter.undertow$undertow_handler$fn$reify__58436 handleRequest "undertow.clj" 40]
  [io.undertow.server.session.SessionAttachmentHandler handleRequest "SessionAttachmentHandler.java" 68]
  [io.undertow.server.Connectors executeRootHandler "Connectors.java" 387]
  [io.undertow.server.HttpServerExchange$1 run "HttpServerExchange.java" 852]
  [org.jboss.threads.ContextClassLoaderSavingRunnable run "ContextClassLoaderSavingRunnable.java" 35]
  [org.jboss.threads.EnhancedQueueExecutor safeRun "EnhancedQueueExecutor.java" 2019]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody doRunTask "EnhancedQueueExecutor.java" 1558]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody run "EnhancedQueueExecutor.java" 1449]
  [org.xnio.XnioWorker$WorkerThreadFactory$1$1 run "XnioWorker.java" 1282]
  [java.lang.Thread run "Thread.java" 840]]}
25-08-26 10:08:26:708 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "6640a1f9-5901-41bf-a519-52c3416388a9", :HostName "1", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-08-26 10:08:26:950 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "56813bcb-0918-4d11-86a9-98746da15518"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-08-26 10:08:27:035 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-08-26 10:08:27:074 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-08-26 10:08:29:432 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_2"}
25-08-26 10:08:29:443 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-08-26 10:08:32:606 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "4e77bbaa-c8b3-4238-bd53-43753f9c34e4", :code 0, :msg "模板生成成功"}
25-08-26 10:08:32:607 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-4e77bbaa-c8b3-4238-bd53-43753f9c34e4 中添加消息 {:ProcessId "4e77bbaa-c8b3-4238-bd53-43753f9c34e4", :code 0, :msg "模板生成成功"}
25-08-26 10:08:32:608 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "4e77bbaa-c8b3-4238-bd53-43753f9c34e4", :code 0, :msg "模板生成成功"}
25-08-26 10:08:33:395 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_408c390a", :SelectedInstCodes []} 
 =============================================================

25-08-26 10:08:33:751 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【默认执行】"}
25-08-26 10:08:33:753 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-78639282-fa6a-4822-b502-d2144342e71a", :State "running"} 
 =============================================================

25-08-26 10:08:34:746 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_2", :ScriptId "b97b03ff-b5c3-4422-ac1f-6540f0f85465", :Result false}
25-08-26 10:08:34:747 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-b97b03ff-b5c3-4422-ac1f-6540f0f85465 中添加消息 {:ProcessId "project_2", :ScriptId "b97b03ff-b5c3-4422-ac1f-6540f0f85465", :Result false}
25-08-26 10:08:34:748 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_2", :ScriptId "b97b03ff-b5c3-4422-ac1f-6540f0f85465", :Result false}
25-08-26 10:08:34:843 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:08:34:846 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【更新试验信息】"}
25-08-26 10:08:34:848 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "running"} 
 =============================================================

25-08-26 10:08:35:038 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 10:08:35:040 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "finished"} 
 =============================================================

25-08-26 10:08:35:096 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-08-26 10:08:35:100 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【联机DAQ】"}
25-08-26 10:08:35:102 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "running"} 
 =============================================================

25-08-26 10:08:35:205 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【默认执行】"}
25-08-26 10:08:35:206 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-78639282-fa6a-4822-b502-d2144342e71a", :State "finished"} 
 =============================================================

25-08-26 10:08:41:463 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "【超级管理员】admin: 更新了【平均负荷】输入变量"}
25-08-26 10:08:41:467 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/input-var 
 - 参数: 
 {:ClassName "project_2", :InputVar {:Unit "unit_cdw13c2640", :Dimension "dimension_Force", :Max -1, :hwKey "", :Mode nil, :FxVariableCode nil, :ActionId nil, :Min -1, :ContactInputCode "", :Code "input_avgload", :Value 1111, :IsConstant false, :Type "Number", :IsOverall false, :SubTaskID nil, :IsCheck false}} 
 =============================================================

25-08-26 10:08:41:837 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_2", :ScriptId "476bd163-0663-4a3a-9207-6459e76a635b", :Result false}
25-08-26 10:08:41:838 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-476bd163-0663-4a3a-9207-6459e76a635b 中添加消息 {:ProcessId "project_2", :ScriptId "476bd163-0663-4a3a-9207-6459e76a635b", :Result false}
25-08-26 10:08:41:839 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_2", :ScriptId "476bd163-0663-4a3a-9207-6459e76a635b", :Result false}
25-08-26 10:08:45:387 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_3954c162", :SelectedInstCodes []} 
 =============================================================

25-08-26 10:08:45:872 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【切换试样】"}
25-08-26 10:08:45:876 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :State "running"} 
 =============================================================

25-08-26 10:08:46:030 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :SubTaskID "onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2", :MsgBody {:Cmd "start", :InstCode "sample_3954c162", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:08:46:034 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【更新试验信息】"}
25-08-26 10:08:46:035 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "running"} 
 =============================================================

25-08-26 10:08:46:094 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【切换试样】"}
25-08-26 10:08:46:096 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :State "finished"} 
 =============================================================

25-08-26 10:08:46:138 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 10:08:46:139 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "finished"} 
 =============================================================

25-08-26 10:09:43:298 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_408c390a", :SelectedInstCodes []} 
 =============================================================

25-08-26 10:09:43:776 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【切换试样】"}
25-08-26 10:09:43:778 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :State "running"} 
 =============================================================

25-08-26 10:09:43:924 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :SubTaskID "onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:09:43:928 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【更新试验信息】"}
25-08-26 10:09:43:930 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "running"} 
 =============================================================

25-08-26 10:09:44:035 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【切换试样】"}
25-08-26 10:09:44:037 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :State "finished"} 
 =============================================================

25-08-26 10:09:44:198 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 10:09:44:200 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "finished"} 
 =============================================================

25-08-26 10:10:02:999 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "【超级管理员】admin: 更新了【平均负荷】输入变量"}
25-08-26 10:10:03:004 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/input-var 
 - 参数: 
 {:ClassName "project_2", :InputVar {:Unit "unit_cdw13c2640", :Dimension "dimension_Force", :Max -1, :hwKey "", :Mode nil, :FxVariableCode nil, :ActionId nil, :Min -1, :ContactInputCode "", :Code "input_avgload", :Value 546, :IsConstant false, :Type "Number", :IsOverall false, :SubTaskID nil, :IsCheck false}} 
 =============================================================

25-08-26 10:10:03:167 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_2", :ScriptId "f1520928-586f-4611-a49b-7b6798124efb", :Result false}
25-08-26 10:10:03:168 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-f1520928-586f-4611-a49b-7b6798124efb 中添加消息 {:ProcessId "project_2", :ScriptId "f1520928-586f-4611-a49b-7b6798124efb", :Result false}
25-08-26 10:10:03:169 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_2", :ScriptId "f1520928-586f-4611-a49b-7b6798124efb", :Result false}
25-08-26 10:10:16:319 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_2】项目"}
25-08-26 10:10:16:323 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【默认执行】"}
25-08-26 10:10:16:328 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 10:10:16:333 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【联机DAQ】"}
25-08-26 10:10:16:335 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "finished"} 
 =============================================================

25-08-26 10:10:16:460 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【切换试样】"}
25-08-26 10:10:18:127 DESKTOP-3BSREDP WARN [clj-backend.db.connections:113] - 销毁 project_2 数据库连接
25-08-26 10:10:18:130 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756115767142_project_2.db 成功
25-08-26 10:10:18:180 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_2 应用性能优化配置
25-08-26 10:10:18:181 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_2 项目库连接
25-08-26 10:10:39:363 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "6640a1f9-5901-41bf-a519-52c3416388a9", :HostName "1", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-08-26 10:10:39:399 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "56813bcb-0918-4d11-86a9-98746da15518"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-08-26 10:10:39:477 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-08-26 10:10:39:508 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-08-26 10:10:39:787 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-08-26 10:10:39:789 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-08-26 10:10:42:283 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_2"}
25-08-26 10:10:42:294 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-08-26 10:10:42:560 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "38c0f334-dc03-438d-928a-3c074fd539b1", :code 0, :msg "模板生成成功"}
25-08-26 10:10:42:561 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-38c0f334-dc03-438d-928a-3c074fd539b1 中添加消息 {:ProcessId "38c0f334-dc03-438d-928a-3c074fd539b1", :code 0, :msg "模板生成成功"}
25-08-26 10:10:42:561 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "38c0f334-dc03-438d-928a-3c074fd539b1", :code 0, :msg "模板生成成功"}
25-08-26 10:10:43:197 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【默认执行】"}
25-08-26 10:10:43:198 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-78639282-fa6a-4822-b502-d2144342e71a", :State "running"} 
 =============================================================

25-08-26 10:10:43:339 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_2", :ScriptId "8af04d38-9aa7-48a1-b168-48b1f12ac467", :Result false}
25-08-26 10:10:43:341 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-8af04d38-9aa7-48a1-b168-48b1f12ac467 中添加消息 {:ProcessId "project_2", :ScriptId "8af04d38-9aa7-48a1-b168-48b1f12ac467", :Result false}
25-08-26 10:10:43:342 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_2", :ScriptId "8af04d38-9aa7-48a1-b168-48b1f12ac467", :Result false}
25-08-26 10:10:43:527 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:10:43:642 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【更新试验信息】"}
25-08-26 10:10:43:643 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "running"} 
 =============================================================

25-08-26 10:10:43:769 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【更新试验信息】"}
25-08-26 10:10:43:771 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "finished"} 
 =============================================================

25-08-26 10:10:43:840 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-08-26 10:10:43:843 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "开始流程【联机DAQ】"}
25-08-26 10:10:43:845 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "running"} 
 =============================================================

25-08-26 10:10:43:977 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 2, :project_name "高频-0825全局变量修改", :content "结束流程【默认执行】"}
25-08-26 10:10:43:980 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_2", :ProcessId "project_2-78639282-fa6a-4822-b502-d2144342e71a", :State "finished"} 
 =============================================================

25-08-26 10:11:04:789 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_3954c162", :SelectedInstCodes ["sample_3954c162" "sample_408c390a"]} 
 =============================================================

25-08-26 10:11:08:342 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_408c390a", :SelectedInstCodes ["sample_3954c162" "sample_408c390a"]} 
 =============================================================

25-08-26 10:11:10:264 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_3954c162", :SelectedInstCodes ["sample_3954c162"]} 
 =============================================================

25-08-26 10:11:17:540 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_408c390a", :SelectedInstCodes ["sample_3954c162" "sample_408c390a"]} 
 =============================================================

25-08-26 10:11:19:927 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_3954c162", :SelectedInstCodes ["sample_3954c162"]} 
 =============================================================

25-08-26 10:11:21:470 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_3954c162", :SelectedInstCodes []} 
 =============================================================

25-08-26 10:11:23:355 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_2", :CurrentInstCode "sample_408c390a", :SelectedInstCodes ["sample_3954c162" "sample_408c390a"]} 
 =============================================================

