import {
    AxisTickStrategies,
    emptyTick,
    emptyFill,
    emptyLine,
    DashedLine,
    SolidLine,
    SolidFill,
    ColorCSS,
    UIBackgrounds,
    MarkerBuilders,
    UIOrigins,
    PointMarkers,
    UILayoutBuilders,
    UIElementBuilders,
    UIDraggingModes,
    UIVisibilityModes,
    AxisPosition,
    LegendBoxBuilders,
    ChartXYTitlePositionOptions,
    ImageFill,
    UIDirections,
    ImageFitMode,
    translatePoint,
    PointShape
} from '@arction/lcjs'

const initSingleChunk = ({
    chartXY, id, showTitle, title, color, content, position, onChunkMarkerPositionChangeRef
}) => {
    // 块标注 - 容器
    const chunkContainer = chartXY.addUIElement(
        UILayoutBuilders.Column
    )
        .setOrigin(UIOrigins.RightTop)
        .setPosition(position ?? { x: 100, y: 100 })
        .setMargin(10)
        // 默认不显示
        .setVisible(false)
        // 开启拖动
        .setDraggingMode(UIDraggingModes.draggable)

    // 标题
    if (showTitle) {
        chunkContainer
            .addElement(UIElementBuilders.TextBox)
            .setText(title)
            .setTextFillStyle(new SolidFill({ color: ColorCSS(color) }))
    }

    content.forEach((item, index) => {
        chunkContainer
            .addElement(UIElementBuilders.TextBox)
            .setText(item)
            .setTextFillStyle(new SolidFill({ color: ColorCSS(color) }))
    })

    chunkContainer.onMouseDragStop((event) => {
        const currentPosition = chunkContainer.getPosition()

        console.log('currentPosition', currentPosition)

        // 触发位置变化回调
        if (onChunkMarkerPositionChangeRef) {
            onChunkMarkerPositionChangeRef.current({
                id,
                position: {
                    x: currentPosition.x,
                    y: currentPosition.y
                }
            })
        }
    })

    return chunkContainer
}

export {
    initSingleChunk
}
