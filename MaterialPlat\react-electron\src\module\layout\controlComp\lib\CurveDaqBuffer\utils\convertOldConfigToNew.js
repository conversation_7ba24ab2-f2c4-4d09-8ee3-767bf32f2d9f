import { v4 as uuidv4 } from 'uuid'

import { SOURCE_TYPE } from '../../CurveDoubleArray/constants/constants'
import { initBufferCurve } from './initCurve'

/**
 * 将老的曲线配置转换为新的配置格式
 * @param {Object} oldConfig - 老的配置对象
 * @returns {Object} 新的配置对象
 */
const convertOldConfigToNew = (oldConfig) => {
    if (!oldConfig) {
        throw new Error('oldConfig is required')
    }

    // 将01值转换为布尔值
    const convertBooleanValue = (value) => {
        if (typeof value === 'boolean') return value
        if (value === 1 || value === '1') return true
        if (value === 0 || value === '0') return false
        return Boolean(value)
    }

    // 映射比例类型
    const mapProportionType = (oldType) => {
        const mapping = {
            extend: 'extend',
            not: 'not',
            all: 'all',
            'data-range': 'data-range',
            'last-range': 'last-range'
        }
        return mapping[oldType] || 'not'
    }

    // 映射线条样式
    const mapLineStyle = (oldStyle) => {
        const mapping = {
            solid: 'solid',
            dashed: 'dashed',
            dotted: 'dotted'
        }
        return mapping[oldStyle] || 'solid'
    }

    // 转换点标签配置
    const convertPointTags = (lineConfig) => {
        if (!lineConfig?.line_tags?.[0]?.array) return []

        return lineConfig.line_tags[0].array.map(tag => ({
            id: uuidv4(),
            color: tag.color || '#000000',
            resultVariableId: tag.id,
            isName: convertBooleanValue(tag.name_flag),
            isAll: convertBooleanValue(tag.all_flag),
            isChunk: convertBooleanValue(tag.chunk_flag),
            sampleCode: tag.sample_code || '',
            isLine: convertBooleanValue(tag.line_flag),
            isSample: convertBooleanValue(tag.sample_flag),
            isVal: convertBooleanValue(tag.val_flag),
            isAbbr: convertBooleanValue(tag.abbr_flag)
        }))
    }

    // 转换块标签配置
    const convertChunkTags = (blockConfig) => {
        if (!blockConfig?.block_tags) return []

        return blockConfig.block_tags.map(block => ({
            id: block.block_id,
            color: block.color || '#000',
            isName: convertBooleanValue(block.name_flag),
            isAll: convertBooleanValue(block.all_flag),
            title: block.name || '',
            showTitle: true,
            isChunk: convertBooleanValue(block.chunk_flag),
            sampleCode: block.sample_code || '',
            isLine: true,
            isSample: convertBooleanValue(block.sample_flag),
            isVal: convertBooleanValue(block.val_flag),
            isAbbr: convertBooleanValue(block.abbr_flag),
            curveIndex: 0,
            results: block.array || []
        }))
    }

    // 转换曲线配置
    const convertCurves = (curveConfig, yChannel, pointTags, sourceType) => {
        const curves = initBufferCurve({
            sourceType
        })

        if (!curveConfig?.[0] || !yChannel?.[0]) {
            return curves
        }

        const curve = curveConfig[0]

        const res = {}

        Object.keys(curves).forEach((key) => {
            res[key] = {
                name: curves[key].name,
                lines: [
                    {
                        isLine: convertBooleanValue(curve.line_open),
                        lineType: mapLineStyle(curve.line_style),
                        lineThickness: curve.line_thickness || 1,
                        isSign: convertBooleanValue(curve.sign_open),
                        signType: curve.sign_style || 'o',
                        signEach: convertBooleanValue(curve.sign_each),
                        color: curve.line_color || '#000000',
                        code: yChannel[0],
                        isApply: convertBooleanValue(oldConfig.apply_point_count),
                        yUnit: oldConfig.y_units?.id || '',
                        id: `Y1轴曲线组-${key}-${yChannel[0]}`,
                        name: `Y1轴曲线组-${oldConfig.data_source_type || '数据源'}[0]-${oldConfig.y_label || 'Y轴'}`,
                        pointTags
                    }
                ]
            }
        })

        return res
    }

    const sourceType = oldConfig.display === '试样' ? SOURCE_TYPE.单数据源 : SOURCE_TYPE.多数据源

    const pointTags = convertPointTags(oldConfig.line_config)
    const chunkTags = convertChunkTags(oldConfig.block_config)
    const curves = convertCurves(oldConfig.curve_config, oldConfig.y_channel, pointTags, sourceType)
    const curves2 = convertCurves(oldConfig.curve_config, oldConfig.y2_channel, pointTags, sourceType)

    // 构建新的配置对象
    const newConfig = {
        base: {
            isName: convertBooleanValue(oldConfig.curve_nama_setting_enabl),
            name: oldConfig.curve_name || '曲线',
            sourceType,
            sourceInputCode: oldConfig.buffer_code || oldConfig.input_code || '',
            updateFreq: 180, // 默认更新频率
            crossInputCode: ''
        },
        curveGroup: {
            yAxis: {
                isEnable: true,
                index: 0,
                name: 'Y1轴曲线组',
                xSignal: oldConfig.x_channel || 'time',
                xUnit: oldConfig.x_units?.id || '',
                ySignal: oldConfig.y_channel || [],
                curves
            },
            y2Axis: {
                isEnable: oldConfig.y2_channel?.length > 0,
                index: 1,
                name: 'Y2轴曲线组',
                xSignal: oldConfig.x_channel || 'time',
                xUnit: oldConfig.x_units?.id || '',
                ySignal: oldConfig.y2_channel || [],
                curves: curves2
            }
        },
        xAxis: {
            name: oldConfig.x_label || 'X轴',
            unit: oldConfig.x_units?.id || '',
            proportionType: mapProportionType(oldConfig.x_proportion),
            lowLimit: oldConfig.x_low_limit || 0,
            upLimit: oldConfig.x_up_limit || 10,
            lastRange: oldConfig.x_last_range || 10,
            isLog: convertBooleanValue(oldConfig.x_log),
            type: mapLineStyle(oldConfig.x_grid_line_style),
            thickness: oldConfig.x_thickness || 2,
            color: oldConfig.x_color || '#000000',
            isGrid: convertBooleanValue(oldConfig.x_grid_line),
            gridType: mapLineStyle(oldConfig.x_grid_line_style),
            gridThickness: oldConfig.x_grid_line_thickness || 1,
            gridColor: oldConfig.x_grid_line_color || '#000000',
            isZeroLine: convertBooleanValue(oldConfig.x_zero_line),
            zeroLineType: mapLineStyle(oldConfig.x_zero_line_style),
            zeroLineThickness: oldConfig.x_zero_line_thickness || 1,
            zeroLineColor: oldConfig.x_zero_line_color || '#000000'
        },
        yAxis: {
            name: oldConfig.y_label || 'Y轴',
            proportionType: mapProportionType(oldConfig.y_proportion),
            lowLimit: oldConfig.y_low_limit || 0,
            upLimit: oldConfig.y_up_limit || 10,
            lastRange: oldConfig.y_last_range || 10,
            isLog: convertBooleanValue(oldConfig.y_log),
            type: 'solid',
            thickness: oldConfig.y_thickness || 2,
            color: oldConfig.y_color || '#000000',
            isGrid: convertBooleanValue(oldConfig.y_grid_line),
            gridType: mapLineStyle(oldConfig.y_grid_line_style),
            gridThickness: oldConfig.y_grid_line_thickness || 1,
            gridColor: oldConfig.y_grid_line_color || '#000000',
            isZeroLine: convertBooleanValue(oldConfig.y_zero_line),
            zeroLineType: mapLineStyle(oldConfig.y_zero_line_style),
            zeroLineThickness: oldConfig.y_zero_line_thickness || 1,
            zeroLineColor: oldConfig.y_zero_line_color || '#000000'
        },
        y2Axis: {
            name: oldConfig.y2_label || 'Y2轴',
            proportionType: mapProportionType(oldConfig.y2_proportion),
            lowLimit: oldConfig.y2_low_limit || 0,
            upLimit: oldConfig.y2_up_limit || 10,
            lastRange: oldConfig.y2_last_range || 10,
            isLog: convertBooleanValue(oldConfig.y2_log),
            type: 'solid',
            thickness: oldConfig.y2_thickness || 2,
            color: oldConfig.y2_color || '#000000',
            isGrid: convertBooleanValue(oldConfig.y2_grid_line),
            gridType: mapLineStyle(oldConfig.y2_grid_line_style),
            gridThickness: oldConfig.y2_grid_line_thickness || 1,
            gridColor: oldConfig.y2_grid_line_color || '#000000',
            isZeroLine: convertBooleanValue(oldConfig.y2_zero_line),
            zeroLineType: mapLineStyle(oldConfig.y2_zero_line_style),
            zeroLineThickness: oldConfig.y2_zero_line_thickness || 1,
            zeroLineColor: oldConfig.y2_zero_line_color || '#000000'
        },
        auxiliary: [],
        legend: {
            open: convertBooleanValue(oldConfig.legend_flag)
        },
        pointTag: {
            open: convertBooleanValue(oldConfig.line_tag_flag)
        },
        chunkTag: {
            open: convertBooleanValue(oldConfig.block_tag_flag),
            list: chunkTags
        },
        marker: [],
        defineAxis: {
            isDefineAxis: convertBooleanValue(oldConfig.coordinate_source_flag),
            inputCode: oldConfig.coordinate_source_input_code || '',
            source: oldConfig.coordinate_source_select || []
        }
    }

    return newConfig
}

export { convertOldConfigToNew }
