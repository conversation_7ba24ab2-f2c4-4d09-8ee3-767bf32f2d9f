import React, { useMemo } from 'react'
import {
    Form, Row, Col, Select, Checkbox, Input
} from 'antd'
import { useTranslation } from 'react-i18next'

import { LINE_STYLE_TYPE, SIGN_STYLE_TYPE, SOURCE_TYPE } from '../../constants'
import CurveSettingButton from './components/curveSettingButton/index'

const { Item, useWatch } = Form

/**
 * 第二步，曲线
 * TODO  等二维数组输入变量
 */
const StepCurve = ({ channels, isBufferCurve }) => {
    const { t } = useTranslation()
    const base = Form.useWatch(['base'])

    return (
        <>
            <div className="step-curve">
                {/* y1轴 */}
                <div className="centre">
                    <div className="right-float">
                        <Item
                            name={['curveGroup', 'yAxis', 'curves']}
                            noStyle
                        >
                            <CurveSettingButton channels={channels} isBufferCurve={isBufferCurve} />
                        </Item>
                    </div>
                    <Item
                        hidden
                        name={['curveGroup', 'yAxis', 'curves']}
                    >
                        <Input />
                    </Item>
                    <Row>
                        <Col hidden span={12}>
                            <Item
                                name={['curveGroup', 'yAxis', 'isEnable']}
                                label={t('启用')}
                                valuePropName="checked"
                            >
                                <Checkbox />
                            </Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Item
                                name={['curveGroup', 'yAxis', 'name']}
                                label={t('曲线分组名称')}
                            >
                                <Input />
                            </Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Item
                                name={['curveGroup', 'yAxis', 'xSignal']}
                                label={t('X轴')}
                            >
                                <Select
                                    fieldNames={{ label: 'name', value: 'code' }}
                                    options={channels}
                                />
                            </Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Form.Item noStyle>
                                <Item
                                    name={['curveGroup', 'yAxis', 'ySignal']}
                                    label="Y1轴"
                                >
                                    <Select
                                        fieldNames={{ label: 'name', value: 'code' }}
                                        mode="multiple"
                                        maxCount={base?.sourceType === SOURCE_TYPE.多数据源 ? 1 : undefined}
                                        options={channels}
                                    />
                                </Item>
                            </Form.Item>
                        </Col>
                    </Row>
                </div>

                {/* y2轴 */}
                <div className="centre">
                    <div className="right-float">
                        <Item
                            name={['curveGroup', 'y2Axis', 'curves']}
                            noStyle
                        >
                            <CurveSettingButton channels={channels} isBufferCurve={isBufferCurve} />
                        </Item>
                    </div>
                    <Item
                        hidden
                        name={['curveGroup', 'y2Axis', 'curves']}
                    >
                        <Input />
                    </Item>
                    <Row>
                        <Col span={12}>
                            <Item
                                name={['curveGroup', 'y2Axis', 'isEnable']}
                                label={t('启用')}
                                valuePropName="checked"
                            >
                                <Checkbox />
                            </Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Item
                                name={['curveGroup', 'y2Axis', 'name']}
                                label={t('曲线分组名称')}
                            >
                                <Input />
                            </Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Item
                                name={['curveGroup', 'y2Axis', 'xSignal']}
                                label={t('X轴')}
                            >
                                <Select
                                    fieldNames={{ label: 'name', value: 'code' }}
                                    options={channels}
                                />
                            </Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Form.Item shouldUpdate noStyle>
                                <Item
                                    name={['curveGroup', 'y2Axis', 'ySignal']}
                                    label="Y2轴"
                                >
                                    <Select
                                        fieldNames={{ label: 'name', value: 'code' }}
                                        mode="multiple"
                                        maxCount={base?.sourceType === SOURCE_TYPE.多数据源 ? 1 : undefined}
                                        options={channels}
                                    />
                                </Item>
                            </Form.Item>
                        </Col>
                    </Row>
                </div>
            </div>
        </>
    )
}

export default StepCurve
