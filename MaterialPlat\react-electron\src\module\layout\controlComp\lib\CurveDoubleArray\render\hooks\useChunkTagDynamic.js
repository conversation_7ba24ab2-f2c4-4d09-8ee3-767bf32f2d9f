import { useEffect, useMemo, useRef } from 'react'
import { useSelector } from 'react-redux'
import { isEqual } from 'lodash'

import { config2ChartOption } from '../utils/config2ChartOption'

const useChunkTagDynamic = ({
    chartOption, config, chartXYRef, compStatus, updateConfig
}) => {
    const currentConfig = useRef()

    const optSample = useSelector(state => state.project.optSample)
    const resultHistoryData = useSelector(state => state.project.resultHistoryData)
    const resultData = useSelector(state => state.template.resultData)

    useEffect(() => {
        currentConfig.current = chartOption.markerPoint.map((i) => ([i.id]))
    }, [chartOption])

    useEffect(() => {
        const { markerChunk } = config2ChartOption(config, compStatus)

        if (isEqual(markerChunk, currentConfig.current)) {
            return
        }

        markerChunk.forEach((i) => {
            const {
                id, showTitle, title, color, content, position
            } = i

            if (isEqual(currentConfig.current?.find((j) => j.id === i.id), i)) {
                return
            }

            chartXYRef.current.updateChunkContent({
                id, showTitle, title, color, content, position
            })
        })

        currentConfig.current = markerChunk
    }, [optSample, resultHistoryData, resultData, compStatus])

    const updateChunkTagPosition = (c) => {
        if (config) {
            const newConfig = {
                ...config,
                compStatus: {
                    ...(compStatus ?? {}),
                    chunkTag: {
                        ...(compStatus?.chunkTag ?? {}),
                        position: {
                            ...(compStatus?.chunkTag?.position ?? {}),
                            // 保存
                            [c.id]: c.position
                        }
                    }
                }
            }

            updateConfig(newConfig)
        }
    }

    return {
        updateChunkTagPosition
    }
}

export default useChunkTagDynamic
