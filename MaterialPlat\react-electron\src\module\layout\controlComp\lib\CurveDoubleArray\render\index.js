import React, {
    useEffect, useMemo, useRef, forwardRef, useImperativeHandle
} from 'react'
import { useHotkeys } from 'react-hotkeys-hook'

import ChartXY from '@/components/charts/ChartXY/index'

import useData from './hooks/useData'
import useCrossSyncInputVar from './hooks/useCrossSyncInputVar'
import useAuxiliaryDynamic from './hooks/useAuxiliaryDynamic'
import usePointTagDynamic from './hooks/usePointTagDynamic'
import useChunkTagDynamic from './hooks/useChunkTagDynamic'
import useChartConfig from './hooks/useChartConfig'
import useSubscribeApi from './hooks/useSubscribeApi'

const Render = forwardRef(({
    isBufferCurve,
    id, config, compStatus, updateConfig,
    openCross, openBreak, showPointTag, showChunkTag, isLocked, isMarking, onMarkingStep
}, ref) => {
    const chartXYRef = useRef()

    const { filteredConfig, chartOption } = useChartConfig({
        config, compStatus, isBufferCurve
    })

    useAuxiliaryDynamic({ chartOption, config: filteredConfig, chartXYRef })

    const { updatePointTagPosition } = usePointTagDynamic({
        chartOption, config, chartXYRef, compStatus, updateConfig
    })

    const { updateChunkTagPosition } = useChunkTagDynamic({
        chartOption, config, chartXYRef, compStatus, updateConfig
    })

    const { targetRef } = useSubscribeApi({
        isBufferCurve,
        id,
        config,
        chartXYRef
    })

    const { highlightLineId } = useData({
        isBufferCurve,
        id,
        config,
        chartOption,
        chartXYRef,
        isLocked
    })

    const { crossPercent, onCrossMove } = useCrossSyncInputVar({
        config
    })

    // 暴露方法给上层组件
    useImperativeHandle(ref, () => ({
        restore: () => {
            chartXYRef.current?.restore?.()
        },
        clearBreakPoint: () => {
            chartXYRef.current?.clearBreakPoint?.()
        }
    }), [])

    // 十字线 ------------------------------------------------------------------------
    useEffect(() => {
        if (openCross) {
            chartXYRef.current?.openCross?.()
        } else {
            chartXYRef.current?.closeCross?.()
        }
    }, [openCross])

    // 断裂点 ------------------------------------------------------------------------
    useEffect(() => {
        if (openBreak) {
            chartXYRef.current?.openCross?.()
        } else {
            chartXYRef.current?.closeCross?.()
        }
    }, [openBreak])

    useHotkeys('Enter', (e) => {
        if (openBreak) {
            // 设置断裂点
            chartXYRef.current?.setBreakPoint?.()
        } else if (isMarking) {
            // 手工标记步骤
            onMarkingStep?.(chartXYRef.current?.getCrossPoint())
        }
    })

    // 显示标签 --------------------------------------------------------------------------
    useEffect(() => {
        if (showPointTag || showChunkTag) {
            chartXYRef.current?.showTag?.()
        } else {
            chartXYRef.current?.hideTag?.()
        }
    }, [showPointTag, showChunkTag, chartOption])

    // 手工标定 --------------------------------------------------------------------------
    useEffect(() => {
        if (isMarking) {
            // 激活十字线等操作
            chartXYRef.current?.openCross?.()
        } else {
            // 如果不是手工标记模式且十字线未手动开启，则关闭十字线
            // eslint-disable-next-line no-lonely-if
            if (!openCross) {
                chartXYRef.current?.closeCross?.()
            }
        }
    }, [isMarking, openCross])

    // 订阅数据
    return (
        <div
            ref={targetRef}
            style={{
                width: '100%',
                height: '100%',
                overflow: 'hidden'
            }}
        >
            <ChartXY
                ref={chartXYRef}
                option={chartOption}
                crossPercent={crossPercent}
                onCrossMove={onCrossMove}
                highlightLineId={highlightLineId}
                onAnnotationPositionChange={updatePointTagPosition}
                onChunkMarkerPositionChange={updateChunkTagPosition}
            />
        </div>
    )
})

export default Render
