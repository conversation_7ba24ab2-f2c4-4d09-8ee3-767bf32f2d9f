2025-08-25 09:11:59,467 [nREPL-session-4affd452-6709-4af4-bb04-7230f3cb2f87] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-25 09:12:00,271 [nREPL-session-4affd452-6709-4af4-bb04-7230f3cb2f87] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-25 09:12:00,468 [nREPL-session-4affd452-6709-4af4-bb04-7230f3cb2f87] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-25 09:12:00,478 [nREPL-session-4affd452-6709-4af4-bb04-7230f3cb2f87] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-25 09:12:00,597 [nREPL-session-4affd452-6709-4af4-bb04-7230f3cb2f87] INFO  org.jboss.threads - J<PERSON><PERSON> Threads version 3.1.0.Final 
2025-08-25 09:12:00,706 [nREPL-session-4affd452-6709-4af4-bb04-7230f3cb2f87] INFO  luminus.http-server - server started on port 3000 
2025-08-25 09:12:00,706 [nREPL-session-4affd452-6709-4af4-bb04-7230f3cb2f87] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-25 09:19:06,392 [XNIO-1 task-3] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:13)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:43)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.hardware.station.service$verify_cfg_mapping.invokeStatic(service.clj:413)
	at clj_backend.modules.hardware.station.service$verify_cfg_mapping.invoke(service.clj:395)
	at clojure.lang.AFn.applyToHelper(AFn.java:156)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.core$apply.invokeStatic(core.clj:667)
	at clojure.core$apply.invoke(core.clj:662)
	at clj_backend.common.template_utils$use_template_execute$fn__19847.invoke(template_utils.clj:85)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.common.template_utils$use_template_execute.invokeStatic(template_utils.clj:84)
	at clj_backend.common.template_utils$use_template_execute.doInvoke(template_utils.clj:77)
	at clojure.lang.RestFn.invoke(RestFn.java:445)
	at clj_backend.modules.hardware.station.routes$routes$fn__40220.invoke(routes.clj:176)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 11:17:07,264 [nREPL-session-63ab34d1-7ca9-4c44-a119-ec3285eaec71] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-25 11:17:08,074 [nREPL-session-63ab34d1-7ca9-4c44-a119-ec3285eaec71] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-25 11:17:08,424 [nREPL-session-63ab34d1-7ca9-4c44-a119-ec3285eaec71] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-25 11:17:08,438 [nREPL-session-63ab34d1-7ca9-4c44-a119-ec3285eaec71] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-25 11:17:08,599 [nREPL-session-63ab34d1-7ca9-4c44-a119-ec3285eaec71] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-25 11:17:08,711 [nREPL-session-63ab34d1-7ca9-4c44-a119-ec3285eaec71] INFO  luminus.http-server - server started on port 3000 
2025-08-25 11:17:08,712 [nREPL-session-63ab34d1-7ca9-4c44-a119-ec3285eaec71] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-25 11:44:59,223 [XNIO-1 task-3] ERROR jdbc.audit - 4. PreparedStatement.execute() PRAGMA wal_checkpoint(PASSIVE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 11:44:59,224 [XNIO-1 task-3] ERROR jdbc.sqltiming - 4. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(PASSIVE) 
 {FAILED after 2 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 11:47:03,975 [XNIO-1 task-3] ERROR jdbc.audit - 4. PreparedStatement.execute() PRAGMA wal_checkpoint(PASSIVE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 11:47:03,976 [XNIO-1 task-3] ERROR jdbc.sqltiming - 4. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(PASSIVE) 
 {FAILED after 0 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 13:45:30,540 [XNIO-1 task-1] ERROR jdbc.audit - 13. PreparedStatement.execute() PRAGMA wal_checkpoint(PASSIVE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 13:45:30,541 [XNIO-1 task-1] ERROR jdbc.sqltiming - 13. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(PASSIVE) 
 {FAILED after 0 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 13:45:42,524 [XNIO-1 task-1] ERROR jdbc.audit - 13. PreparedStatement.execute() PRAGMA wal_checkpoint(PASSIVE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 13:45:42,525 [XNIO-1 task-1] ERROR jdbc.sqltiming - 13. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(PASSIVE) 
 {FAILED after 0 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 13:47:02,352 [XNIO-1 task-2] ERROR jdbc.audit - 13. PreparedStatement.execute() PRAGMA wal_checkpoint(PASSIVE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 13:47:02,353 [XNIO-1 task-2] ERROR jdbc.sqltiming - 13. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(PASSIVE) 
 {FAILED after 0 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 14:01:33,268 [nREPL-session-1f598894-71c0-4dbc-aded-0aa34c18f4c0] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-25 14:01:34,111 [nREPL-session-1f598894-71c0-4dbc-aded-0aa34c18f4c0] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-25 14:01:34,401 [nREPL-session-1f598894-71c0-4dbc-aded-0aa34c18f4c0] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-25 14:01:34,419 [nREPL-session-1f598894-71c0-4dbc-aded-0aa34c18f4c0] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-25 14:01:34,573 [nREPL-session-1f598894-71c0-4dbc-aded-0aa34c18f4c0] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-25 14:01:34,678 [nREPL-session-1f598894-71c0-4dbc-aded-0aa34c18f4c0] INFO  luminus.http-server - server started on port 3000 
2025-08-25 14:01:34,678 [nREPL-session-1f598894-71c0-4dbc-aded-0aa34c18f4c0] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-25 14:22:48,828 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-25 14:22:49,622 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-25 14:22:49,964 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-25 14:22:49,999 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-25 14:22:50,162 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-25 14:22:50,294 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  luminus.http-server - server started on port 3000 
2025-08-25 14:22:50,294 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-25 15:50:25,383 [XNIO-1 task-5] ERROR jdbc.audit - 24. PreparedStatement.execute() PRAGMA wal_checkpoint(PASSIVE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 15:50:25,385 [XNIO-1 task-5] ERROR jdbc.sqltiming - 24. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(PASSIVE) 
 {FAILED after 8 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 17:27:45,371 [XNIO-1 task-9] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:52)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:37)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:25)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.modules.hardware.station.service$host_instance_handle.invokeStatic(service.clj:536)
	at clj_backend.modules.hardware.station.service$host_instance_handle.invoke(service.clj:522)
	at clj_backend.modules.sys.user.sys_user_service$login.invokeStatic(sys_user_service.clj:96)
	at clj_backend.modules.sys.user.sys_user_service$login.invoke(sys_user_service.clj:91)
	at clj_backend.modules.sys.user.sys_user_routes$routes$fn__43153.invoke(sys_user_routes.clj:56)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58436.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 17:55:17,677 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Starting migrations 
2025-08-25 17:55:17,711 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] WARN  migratus.migrations - skipping: '29990101000000-add-subtask-init-data.up.sql - 快捷方式.lnk' migrations must match pattern: ^(\d+)-([^\.]+)((?:\.[^\.]+)+)$ 
2025-08-25 17:55:17,713 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] WARN  migratus.migrations - skipping: '29990101000001-add-sample-init-data.up.sql - 快捷方式.lnk' migrations must match pattern: ^(\d+)-([^\.]+)((?:\.[^\.]+)+)$ 
2025-08-25 17:55:17,715 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Running down for [29990101000001 29990101000000 20250519103156 20250325023407 20250321030015 20250312075213 20250304075728 20250304062051 20250225034224 20250217075221 20250217074603 20250214102123 20250213100203 20250213032140 20250110010951 20250103022555 20250101000000 20241225024444 20241210033352 20241114094737 20241109033358 20241029011329 20240418023711 20240409025215 20240407022048 20240328015431 20240318031213 20240317141418 20231111055444 20231018032209 20230627085851 20230615090452 20230531064426 20230509013720 20230421090947 20230324070914 20230316060040 20230309054138 20230301080547 20230227115525 20230224024126 20230222081921] 
2025-08-25 17:55:17,716 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 29990101000001-add-sample-init-data 
2025-08-25 17:55:17,716 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,724 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 29990101000000-add-subtask-init-data 
2025-08-25 17:55:17,724 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,726 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250519103156-add-input-variable-boolean-tab 
2025-08-25 17:55:17,726 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,728 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250325023407-add-field-sample-param 
2025-08-25 17:55:17,728 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,731 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250321030015-add-feild-description 
2025-08-25 17:55:17,732 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,733 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250312075213-add-feild-project 
2025-08-25 17:55:17,733 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,735 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250304075728-add-sample-parameter-col 
2025-08-25 17:55:17,735 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,737 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250304062051-update-inspection-table 
2025-08-25 17:55:17,737 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,738 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250225034224-add-system-config 
2025-08-25 17:55:17,738 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,746 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250217075221-add-field-input-variable 
2025-08-25 17:55:17,746 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,748 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250217074603-add-field-guide-dialog 
2025-08-25 17:55:17,748 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,749 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250214102123-update-station-or-home-layout-config-datas 
2025-08-25 17:55:17,749 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,751 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250213100203-add-field-station-project-table 
2025-08-25 17:55:17,751 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,753 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250213032140-create-station-or-home-layout-config-table 
2025-08-25 17:55:17,753 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,754 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250110010951-create-inspection-tables 
2025-08-25 17:55:17,754 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,758 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250103022555-update-hardware-axis-device-table-fields 
2025-08-25 17:55:17,758 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,759 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250101000000-add-hardware-id-mapping 
2025-08-25 17:55:17,759 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,760 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241225024444-add-control-library-tables 
2025-08-25 17:55:17,760 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,788 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241210033352-add-hardware-ccss-fields 
2025-08-25 17:55:17,788 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,789 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241114094737-add-project-temporary-flag 
2025-08-25 17:55:17,790 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,791 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241109033358-init-audio-data 
2025-08-25 17:55:17,791 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,793 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241029011329-create-system-time-table 
2025-08-25 17:55:17,793 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,795 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240418023711-add-field-default-cfg-station 
2025-08-25 17:55:17,795 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,796 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240409025215-add-field-state-project-station 
2025-08-25 17:55:17,796 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,798 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240407022048-add-station-project 
2025-08-25 17:55:17,798 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,799 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240328015431-add-col-sample-parameter 
2025-08-25 17:55:17,799 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,800 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240318031213-add-log 
2025-08-25 17:55:17,800 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,804 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240317141418-add-system-version-table 
2025-08-25 17:55:17,804 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,805 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20231111055444-add-station 
2025-08-25 17:55:17,805 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,807 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20231018032209-add-audio-table 
2025-08-25 17:55:17,807 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,808 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230627085851-add-module-datasource-table 
2025-08-25 17:55:17,808 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,809 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230615090452-add-project-table 
2025-08-25 17:55:17,809 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,810 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230531064426-add-picture-table 
2025-08-25 17:55:17,810 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,815 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230509013720-add-internal-code-table 
2025-08-25 17:55:17,816 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,818 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230421090947-add-user-tables 
2025-08-25 17:55:17,818 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,820 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230324070914-add-hardware-tables 
2025-08-25 17:55:17,820 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,823 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230316060040-add-template-table 
2025-08-25 17:55:17,823 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,824 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230309054138-add-function-table 
2025-08-25 17:55:17,824 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,826 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230301080547-add-binder-tables 
2025-08-25 17:55:17,826 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,829 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230227115525-add-sample-tables 
2025-08-25 17:55:17,829 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,831 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230224024126-add-international-table 
2025-08-25 17:55:17,831 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,832 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230222081921-add-unit-tables 
2025-08-25 17:55:17,832 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x20f7700a net.sf.log4jdbc.ConnectionSpy@20f7700a]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,835 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Ending migrations 
2025-08-25 17:55:17,836 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Starting migrations 
2025-08-25 17:55:17,855 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] WARN  migratus.migrations - skipping: '29990101000000-add-subtask-init-data.up.sql - 快捷方式.lnk' migrations must match pattern: ^(\d+)-([^\.]+)((?:\.[^\.]+)+)$ 
2025-08-25 17:55:17,856 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] WARN  migratus.migrations - skipping: '29990101000001-add-sample-init-data.up.sql - 快捷方式.lnk' migrations must match pattern: ^(\d+)-([^\.]+)((?:\.[^\.]+)+)$ 
2025-08-25 17:55:17,857 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Running up for [20230222081921 20230224024126 20230227115525 20230301080547 20230309054138 20230316060040 20230324070914 20230421090947 20230509013720 20230531064426 20230615090452 20230627085851 20231018032209 20231111055444 20240317141418 20240318031213 20240328015431 20240407022048 20240409025215 20240418023711 20241029011329 20241109033358 20241114094737 20241210033352 20241225024444 20250101000000 20250103022555 20250110010951 20250213032140 20250213100203 20250214102123 20250217074603 20250217075221 20250225034224 20250304062051 20250304075728 20250312075213 20250321030015 20250325023407 20250519103156 29990101000000 29990101000001] 
2025-08-25 17:55:17,857 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230222081921-add-unit-tables 
2025-08-25 17:55:17,857 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,867 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230224024126-add-international-table 
2025-08-25 17:55:17,867 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,868 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230227115525-add-sample-tables 
2025-08-25 17:55:17,869 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,872 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230301080547-add-binder-tables 
2025-08-25 17:55:17,872 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,875 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230309054138-add-function-table 
2025-08-25 17:55:17,875 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,878 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230316060040-add-template-table 
2025-08-25 17:55:17,878 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,882 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230324070914-add-hardware-tables 
2025-08-25 17:55:17,882 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,886 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230421090947-add-user-tables 
2025-08-25 17:55:17,886 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,900 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230509013720-add-internal-code-table 
2025-08-25 17:55:17,900 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,902 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230531064426-add-picture-table 
2025-08-25 17:55:17,902 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,921 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230615090452-add-project-table 
2025-08-25 17:55:17,922 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,924 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230627085851-add-module-datasource-table 
2025-08-25 17:55:17,924 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,925 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20231018032209-add-audio-table 
2025-08-25 17:55:17,926 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,927 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20231111055444-add-station 
2025-08-25 17:55:17,927 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,931 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240317141418-add-system-version-table 
2025-08-25 17:55:17,931 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,934 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240318031213-add-log 
2025-08-25 17:55:17,934 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,936 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240328015431-add-col-sample-parameter 
2025-08-25 17:55:17,936 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,939 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240407022048-add-station-project 
2025-08-25 17:55:17,939 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,940 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240409025215-add-field-state-project-station 
2025-08-25 17:55:17,942 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,943 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240418023711-add-field-default-cfg-station 
2025-08-25 17:55:17,943 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,945 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241029011329-create-system-time-table 
2025-08-25 17:55:17,945 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,947 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241109033358-init-audio-data 
2025-08-25 17:55:17,947 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,964 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241114094737-add-project-temporary-flag 
2025-08-25 17:55:17,964 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,966 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241210033352-add-hardware-ccss-fields 
2025-08-25 17:55:17,967 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,976 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241225024444-add-control-library-tables 
2025-08-25 17:55:17,976 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,979 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250101000000-add-hardware-id-mapping 
2025-08-25 17:55:17,979 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,981 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250103022555-update-hardware-axis-device-table-fields 
2025-08-25 17:55:17,981 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,983 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250110010951-create-inspection-tables 
2025-08-25 17:55:17,983 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,986 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250213032140-create-station-or-home-layout-config-table 
2025-08-25 17:55:17,986 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,989 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250213100203-add-field-station-project-table 
2025-08-25 17:55:17,989 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,990 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250214102123-update-station-or-home-layout-config-datas 
2025-08-25 17:55:17,990 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,993 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250217074603-add-field-guide-dialog 
2025-08-25 17:55:17,993 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:17,996 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250217075221-add-field-input-variable 
2025-08-25 17:55:17,997 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,000 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250225034224-add-system-config 
2025-08-25 17:55:18,000 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,001 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250304062051-update-inspection-table 
2025-08-25 17:55:18,002 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,013 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250304075728-add-sample-parameter-col 
2025-08-25 17:55:18,013 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,016 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250312075213-add-feild-project 
2025-08-25 17:55:18,016 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,019 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250321030015-add-feild-description 
2025-08-25 17:55:18,019 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,022 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250325023407-add-field-sample-param 
2025-08-25 17:55:18,022 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,025 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250519103156-add-input-variable-boolean-tab 
2025-08-25 17:55:18,025 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,026 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 29990101000000-add-subtask-init-data 
2025-08-25 17:55:18,026 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,285 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 29990101000001-add-sample-init-data 
2025-08-25 17:55:18,285 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x550fe592 net.sf.log4jdbc.ConnectionSpy@550fe592]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,314 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Ending migrations 
2025-08-25 17:55:18,315 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Starting migrations 
2025-08-25 17:55:18,342 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Running down for [20250806012558 20250803121942 20250801024809 20250702061611 20250627061857 20250516031245 20250427053604 20250417011824 20250325023426 20250321024314 20250317085159 20250312075000 20250312013359 20250306081753 20250306060336 20250304075812 20250303075133 20250227091306 20250224035012 20250221023540 20250219093040 20250212085342 20250211093937 20250211033310 20250208034153 20250121060754 20250121031956 20250120072613 20250117101718 20250115012653 20250113083200 20241231021048 20241226030758 20241119020534 20241108092239 20241105102712 20241014054607 20241011015022 20240823024703 20240819101526 20240730055736 20240516072246 20240509121358 20240422034811 20240408082118 20240401030938 20240327123609 20240327071526 20240325082816 20240319020246 20240313090755 20240309061427 20240308070540 20240123084214 20240104163312 20231207023335 20231207021356 20231018032209 20230912032834 20230908015613 20230816083119 20230815065225 20230620055337 20230614060018 20230509013720 20230505065622 20230426092633 20230324070914 20230315030952 20230315020002 20230313074247 20230311074534 20230310060839 20230309054138 20230308061505 20230308031706 20230306073201 20230303030427 20230301080547 20230227115525 20230222081921] 
2025-08-25 17:55:18,342 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250806012558-add-export-excel-double-array 
2025-08-25 17:55:18,342 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,347 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250803121942-add-binder-is-lock 
2025-08-25 17:55:18,347 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,353 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250801024809-add-atom-comp 
2025-08-25 17:55:18,353 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,357 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250702061611-add-navbar-login-check 
2025-08-25 17:55:18,357 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,360 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250627061857-add-static-curve-col 
2025-08-25 17:55:18,360 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,366 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250516031245-add-input-variable-boolean-tab 
2025-08-25 17:55:18,366 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,371 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250427053604-add-widght 
2025-08-25 17:55:18,371 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,375 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250417011824-add-static-curve-col 
2025-08-25 17:55:18,375 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,378 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250325023426-add-field-sample-param 
2025-08-25 17:55:18,378 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,383 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250321024314-add-feild-description 
2025-08-25 17:55:18,383 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,387 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250317085159-add-widght-data 
2025-08-25 17:55:18,387 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,390 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250312075000-add-template-comparison-signature-table 
2025-08-25 17:55:18,390 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,395 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250312013359-add-widght-data 
2025-08-25 17:55:18,396 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,399 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250306081753-update-widght-data 
2025-08-25 17:55:18,401 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,404 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250306060336-add-widght-data 
2025-08-25 17:55:18,404 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,408 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250304075812-add-sample-parameter-col 
2025-08-25 17:55:18,408 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,413 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250303075133-add-widght-data 
2025-08-25 17:55:18,413 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,417 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250227091306-update-widget-data 
2025-08-25 17:55:18,417 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,421 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250224035012-add-dynamic-curve 
2025-08-25 17:55:18,422 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,425 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250221023540-add-feild-action 
2025-08-25 17:55:18,425 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,430 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250219093040-add-widght-data 
2025-08-25 17:55:18,430 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,435 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250212085342-add-widget-data-source-table 
2025-08-25 17:55:18,435 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,440 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250211093937-add-static-curve-point-count 
2025-08-25 17:55:18,440 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,444 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250211033310-add-auxiliary-line-fields 
2025-08-25 17:55:18,444 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,449 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250208034153-add-feild-widget-binder 
2025-08-25 17:55:18,449 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,455 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250121060754-add-action-execution-timing-field 
2025-08-25 17:55:18,455 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,459 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250121031956-add-feild-widget 
2025-08-25 17:55:18,459 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,465 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250120072613-add-double-array-curve-table 
2025-08-25 17:55:18,465 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,470 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250117101718-add-instal-widget 
2025-08-25 17:55:18,471 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,476 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250115012653-add-field-input-variable 
2025-08-25 17:55:18,476 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,482 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250113083200-add-field-dialog-binder 
2025-08-25 17:55:18,482 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,486 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241231021048-add-global-project-mapping-table 
2025-08-25 17:55:18,486 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,492 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241226030758-add-control-library-table-version-col 
2025-08-25 17:55:18,492 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,496 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241119020534-add-sample-inst-checked 
2025-08-25 17:55:18,496 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,502 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241108092239-add-table-col 
2025-08-25 17:55:18,502 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,505 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241105102712-add-col-shortcut 
2025-08-25 17:55:18,505 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,510 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241014054607-add-virtual-signal-var-reset-cols 
2025-08-25 17:55:18,512 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,516 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20241011015022-add-curve-marking-cols 
2025-08-25 17:55:18,516 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,521 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240823024703-add-shortcut-col-tip 
2025-08-25 17:55:18,521 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,525 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240819101526-add-sample-instance-update-time 
2025-08-25 17:55:18,525 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,528 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240730055736-add-auxiliary-line 
2025-08-25 17:55:18,528 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,534 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240516072246-add-report-table-csv-cols 
2025-08-25 17:55:18,534 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,539 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240509121358-add-widget-pid 
2025-08-25 17:55:18,539 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,542 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240422034811-add-field-t-mapping 
2025-08-25 17:55:18,542 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,548 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240408082118-add-field-t-action 
2025-08-25 17:55:18,548 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,553 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240401030938-add-field-t-signal-variable 
2025-08-25 17:55:18,553 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,557 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240327123609-add-page-window-property 
2025-08-25 17:55:18,558 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,562 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240327071526-add-col-sample-parameter 
2025-08-25 17:55:18,562 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,566 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240325082816-add-col-t-signal-variable 
2025-08-25 17:55:18,566 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,570 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240319020246-add-col-t-passage-form 
2025-08-25 17:55:18,570 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,573 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240313090755-add-action-table-primary-key 
2025-08-25 17:55:18,574 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,577 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240309061427-add-dynamic-curve 
2025-08-25 17:55:18,577 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,584 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240308070540-add-test-result 
2025-08-25 17:55:18,584 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,588 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240123084214-add-subtask-table 
2025-08-25 17:55:18,588 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,593 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240104163312-add-dongtai-config 
2025-08-25 17:55:18,593 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,597 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20231207023335-add-sample-variable-table 
2025-08-25 17:55:18,597 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,602 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20231207021356-add-export-table 
2025-08-25 17:55:18,602 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,606 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20231018032209-add-video-table 
2025-08-25 17:55:18,608 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,611 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230912032834-add-daq 
2025-08-25 17:55:18,611 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,617 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230908015613-add-page-table 
2025-08-25 17:55:18,617 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,621 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230816083119-table-config 
2025-08-25 17:55:18,621 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,625 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230815065225-add-t-static-curve-table 
2025-08-25 17:55:18,625 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,630 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230620055337-add-t-action-table 
2025-08-25 17:55:18,630 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,636 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230614060018-add-control-library 
2025-08-25 17:55:18,636 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,640 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230509013720-add-internal-code-table 
2025-08-25 17:55:18,640 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,645 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230505065622-teat-migrate 
2025-08-25 17:55:18,645 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,651 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230426092633-add-scheduler-context-col 
2025-08-25 17:55:18,651 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,654 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230324070914-add-hardware-tables 
2025-08-25 17:55:18,654 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,658 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230315030952-add-sample-instance-about 
2025-08-25 17:55:18,658 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,663 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230315020002-add-flow-chart-data 
2025-08-25 17:55:18,663 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,669 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230313074247-add-simple-instance 
2025-08-25 17:55:18,669 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,675 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230311074534-add-shortcut-table 
2025-08-25 17:55:18,675 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,682 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230310060839-add-passage-form-table 
2025-08-25 17:55:18,682 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,687 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230309054138-add-function-table 
2025-08-25 17:55:18,687 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,692 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230308061505-add-input-variable-table 
2025-08-25 17:55:18,692 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,697 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230308031706-add-signal-variable-table 
2025-08-25 17:55:18,697 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,703 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230306073201-add-result-variable-table 
2025-08-25 17:55:18,703 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,709 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230303030427-add-guide-tables 
2025-08-25 17:55:18,709 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,715 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230301080547-add-binder-tables 
2025-08-25 17:55:18,716 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,722 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230227115525-add-sample-tables 
2025-08-25 17:55:18,722 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,730 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230222081921-add-unit-tables 
2025-08-25 17:55:18,730 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6ed969af org.sqlite.jdbc4.JDBC4Connection@6ed969af]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,735 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Ending migrations 
2025-08-25 17:55:18,737 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Starting migrations 
2025-08-25 17:55:18,756 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Running up for [20230222081921 20230227115525 20230301080547 20230303030427 20230306073201 20230308031706 20230308061505 20230309054138 20230310060839 20230311074534 20230313074247 20230315020002 20230315030952 20230324070914 20230426092633 20230505065622 20230509013720 20230614060018 20230620055337 20230815065225 20230816083119 20230908015613 20230912032834 20231018032209 20231207021356 20231207023335 20240104163312 20240123084214 20240308070540 20240309061427 20240313090755 20240319020246 20240325082816 20240327071526 20240327123609 20240401030938 20240408082118 20240422034811 20240509121358 20240516072246 20240730055736 20240819101526 20240823024703 20241011015022 20241014054607 20241105102712 20241108092239 20241119020534 20241226030758 20241231021048 20250113083200 20250115012653 20250117101718 20250120072613 20250121031956 20250121060754 20250208034153 20250211033310 20250211093937 20250212085342 20250219093040 20250221023540 20250224035012 20250227091306 20250303075133 20250304075812 20250306060336 20250306081753 20250312013359 20250312075000 20250317085159 20250321024314 20250325023426 20250417011824 20250427053604 20250516031245 20250627061857 20250702061611 20250801024809 20250803121942 20250806012558] 
2025-08-25 17:55:18,756 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230222081921-add-unit-tables 
2025-08-25 17:55:18,756 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,762 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230227115525-add-sample-tables 
2025-08-25 17:55:18,762 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,769 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230301080547-add-binder-tables 
2025-08-25 17:55:18,770 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,777 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230303030427-add-guide-tables 
2025-08-25 17:55:18,777 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,785 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230306073201-add-result-variable-table 
2025-08-25 17:55:18,785 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,793 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230308031706-add-signal-variable-table 
2025-08-25 17:55:18,793 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,799 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230308061505-add-input-variable-table 
2025-08-25 17:55:18,801 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,808 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230309054138-add-function-table 
2025-08-25 17:55:18,808 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,814 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230310060839-add-passage-form-table 
2025-08-25 17:55:18,814 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,822 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230311074534-add-shortcut-table 
2025-08-25 17:55:18,822 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,828 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230313074247-add-simple-instance 
2025-08-25 17:55:18,828 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,836 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230315020002-add-flow-chart-data 
2025-08-25 17:55:18,836 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,844 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230315030952-add-sample-instance-about 
2025-08-25 17:55:18,844 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,854 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230324070914-add-hardware-tables 
2025-08-25 17:55:18,854 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,862 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230426092633-add-scheduler-context-col 
2025-08-25 17:55:18,862 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,871 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230505065622-teat-migrate 
2025-08-25 17:55:18,871 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,878 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230509013720-add-internal-code-table 
2025-08-25 17:55:18,878 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,885 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230614060018-add-control-library 
2025-08-25 17:55:18,885 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,892 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230620055337-add-t-action-table 
2025-08-25 17:55:18,892 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,900 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230815065225-add-t-static-curve-table 
2025-08-25 17:55:18,900 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,905 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230816083119-table-config 
2025-08-25 17:55:18,905 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,913 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230908015613-add-page-table 
2025-08-25 17:55:18,914 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,921 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230912032834-add-daq 
2025-08-25 17:55:18,921 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,928 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20231018032209-add-video-table 
2025-08-25 17:55:18,928 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,935 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20231207021356-add-export-table 
2025-08-25 17:55:18,935 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,942 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20231207023335-add-sample-variable-table 
2025-08-25 17:55:18,942 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,947 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240104163312-add-dongtai-config 
2025-08-25 17:55:18,947 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,952 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240123084214-add-subtask-table 
2025-08-25 17:55:18,952 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,957 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240308070540-add-test-result 
2025-08-25 17:55:18,957 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,961 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240309061427-add-dynamic-curve 
2025-08-25 17:55:18,961 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,967 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240313090755-add-action-table-primary-key 
2025-08-25 17:55:18,967 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,974 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240319020246-add-col-t-passage-form 
2025-08-25 17:55:18,975 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,978 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240325082816-add-col-t-signal-variable 
2025-08-25 17:55:18,979 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,984 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240327071526-add-col-sample-parameter 
2025-08-25 17:55:18,984 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,988 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240327123609-add-page-window-property 
2025-08-25 17:55:18,988 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,993 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240401030938-add-field-t-signal-variable 
2025-08-25 17:55:18,993 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:18,997 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240408082118-add-field-t-action 
2025-08-25 17:55:18,998 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,002 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240422034811-add-field-t-mapping 
2025-08-25 17:55:19,002 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,007 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240509121358-add-widget-pid 
2025-08-25 17:55:19,007 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,012 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240516072246-add-report-table-csv-cols 
2025-08-25 17:55:19,012 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,018 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240730055736-add-auxiliary-line 
2025-08-25 17:55:19,018 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,022 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240819101526-add-sample-instance-update-time 
2025-08-25 17:55:19,022 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,027 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240823024703-add-shortcut-col-tip 
2025-08-25 17:55:19,027 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,032 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241011015022-add-curve-marking-cols 
2025-08-25 17:55:19,032 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,040 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241014054607-add-virtual-signal-var-reset-cols 
2025-08-25 17:55:19,040 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,045 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241105102712-add-col-shortcut 
2025-08-25 17:55:19,045 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,049 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241108092239-add-table-col 
2025-08-25 17:55:19,049 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,054 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241119020534-add-sample-inst-checked 
2025-08-25 17:55:19,054 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,059 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241226030758-add-control-library-table-version-col 
2025-08-25 17:55:19,059 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,064 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20241231021048-add-global-project-mapping-table 
2025-08-25 17:55:19,064 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,069 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250113083200-add-field-dialog-binder 
2025-08-25 17:55:19,069 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,075 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250115012653-add-field-input-variable 
2025-08-25 17:55:19,075 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,080 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250117101718-add-instal-widget 
2025-08-25 17:55:19,080 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,085 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250120072613-add-double-array-curve-table 
2025-08-25 17:55:19,085 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,089 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250121031956-add-feild-widget 
2025-08-25 17:55:19,089 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,093 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250121060754-add-action-execution-timing-field 
2025-08-25 17:55:19,094 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,099 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250208034153-add-feild-widget-binder 
2025-08-25 17:55:19,099 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,104 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250211033310-add-auxiliary-line-fields 
2025-08-25 17:55:19,104 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,109 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250211093937-add-static-curve-point-count 
2025-08-25 17:55:19,109 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,114 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250212085342-add-widget-data-source-table 
2025-08-25 17:55:19,114 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,118 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250219093040-add-widght-data 
2025-08-25 17:55:19,118 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,123 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250221023540-add-feild-action 
2025-08-25 17:55:19,123 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,127 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250224035012-add-dynamic-curve 
2025-08-25 17:55:19,127 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,135 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250227091306-update-widget-data 
2025-08-25 17:55:19,135 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,139 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250303075133-add-widght-data 
2025-08-25 17:55:19,139 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,143 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250304075812-add-sample-parameter-col 
2025-08-25 17:55:19,144 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,149 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250306060336-add-widght-data 
2025-08-25 17:55:19,149 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,153 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250306081753-update-widght-data 
2025-08-25 17:55:19,153 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,157 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250312013359-add-widght-data 
2025-08-25 17:55:19,157 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,161 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250312075000-add-template-comparison-signature-table 
2025-08-25 17:55:19,161 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,165 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250317085159-add-widght-data 
2025-08-25 17:55:19,166 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,170 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250321024314-add-feild-description 
2025-08-25 17:55:19,170 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,177 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250325023426-add-field-sample-param 
2025-08-25 17:55:19,177 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,182 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250417011824-add-static-curve-col 
2025-08-25 17:55:19,182 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,188 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250427053604-add-widght 
2025-08-25 17:55:19,188 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,192 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250516031245-add-input-variable-boolean-tab 
2025-08-25 17:55:19,192 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,198 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250627061857-add-static-curve-col 
2025-08-25 17:55:19,198 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,202 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250702061611-add-navbar-login-check 
2025-08-25 17:55:19,202 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,207 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250801024809-add-atom-comp 
2025-08-25 17:55:19,207 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,212 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250803121942-add-binder-is-lock 
2025-08-25 17:55:19,212 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,217 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250806012558-add-export-excel-double-array 
2025-08-25 17:55:19,217 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x11af8006 org.sqlite.jdbc4.JDBC4Connection@11af8006]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,223 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Ending migrations 
2025-08-25 17:55:19,223 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Starting migrations 
2025-08-25 17:55:19,227 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Running down for [20250403023041 20250303013738 20250228022223 20240812031107 20240515084445 20240325093726 20230222081921] 
2025-08-25 17:55:19,227 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250403023041-add-log-control-table 
2025-08-25 17:55:19,227 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x2cec103d org.sqlite.jdbc4.JDBC4Connection@2cec103d]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,232 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250303013738-add-double-array-save-table 
2025-08-25 17:55:19,232 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x2cec103d org.sqlite.jdbc4.JDBC4Connection@2cec103d]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,235 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20250228022223-add-creep-tf-special-point 
2025-08-25 17:55:19,235 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x2cec103d org.sqlite.jdbc4.JDBC4Connection@2cec103d]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,240 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240812031107-drop-daq-tables 
2025-08-25 17:55:19,240 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x2cec103d org.sqlite.jdbc4.JDBC4Connection@2cec103d]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,243 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240515084445-add-daqs-table 
2025-08-25 17:55:19,243 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x2cec103d org.sqlite.jdbc4.JDBC4Connection@2cec103d]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,248 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20240325093726-add-messages-daq-copy-table 
2025-08-25 17:55:19,248 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x2cec103d org.sqlite.jdbc4.JDBC4Connection@2cec103d]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,253 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Down 20230222081921-add-table 
2025-08-25 17:55:19,253 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x2cec103d org.sqlite.jdbc4.JDBC4Connection@2cec103d]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,257 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Ending migrations 
2025-08-25 17:55:19,257 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Starting migrations 
2025-08-25 17:55:19,259 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Running up for [20230222081921 20240325093726 20240515084445 20240812031107 20250228022223 20250303013738 20250403023041] 
2025-08-25 17:55:19,259 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20230222081921-add-table 
2025-08-25 17:55:19,259 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x70734f74 org.sqlite.jdbc4.JDBC4Connection@70734f74]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,264 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240325093726-add-messages-daq-copy-table 
2025-08-25 17:55:19,264 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x70734f74 org.sqlite.jdbc4.JDBC4Connection@70734f74]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,269 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240515084445-add-daqs-table 
2025-08-25 17:55:19,269 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x70734f74 org.sqlite.jdbc4.JDBC4Connection@70734f74]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,274 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20240812031107-drop-daq-tables 
2025-08-25 17:55:19,274 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x70734f74 org.sqlite.jdbc4.JDBC4Connection@70734f74]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,278 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250228022223-add-creep-tf-special-point 
2025-08-25 17:55:19,278 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x70734f74 org.sqlite.jdbc4.JDBC4Connection@70734f74]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,284 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250303013738-add-double-array-save-table 
2025-08-25 17:55:19,284 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x70734f74 org.sqlite.jdbc4.JDBC4Connection@70734f74]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,287 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Up 20250403023041-add-log-control-table 
2025-08-25 17:55:19,287 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x70734f74 org.sqlite.jdbc4.JDBC4Connection@70734f74]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-08-25 17:55:19,292 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  migratus.core - Ending migrations 
2025-08-25 17:55:21,467 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  io.undertow - stopping server: Undertow - 2.2.20.Final 
2025-08-25 17:55:21,477 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  luminus.http-server - HTTP server stopped 
2025-08-25 17:55:21,477 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  clj-backend.core - Stopping monitoring server 
2025-08-25 17:55:21,482 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  clj-backend.env - 
-=[clj-backend has shut down successfully]=- 
2025-08-25 17:55:21,513 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-25 17:55:21,982 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-25 17:55:22,066 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-25 17:55:22,104 [nREPL-session-528ed72f-0fdc-4d2e-9ae7-45eeafbf70ef] INFO  luminus.http-server - server started on port 3000 
2025-08-25 19:09:35,415 [nREPL-session-b57cd551-7531-43fc-9dc9-d0a056494868] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-25 19:09:36,239 [nREPL-session-b57cd551-7531-43fc-9dc9-d0a056494868] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-25 19:09:36,512 [nREPL-session-b57cd551-7531-43fc-9dc9-d0a056494868] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-25 19:09:36,541 [nREPL-session-b57cd551-7531-43fc-9dc9-d0a056494868] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-25 19:09:36,677 [nREPL-session-b57cd551-7531-43fc-9dc9-d0a056494868] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-25 19:09:36,785 [nREPL-session-b57cd551-7531-43fc-9dc9-d0a056494868] INFO  luminus.http-server - server started on port 3000 
2025-08-25 19:09:36,786 [nREPL-session-b57cd551-7531-43fc-9dc9-d0a056494868] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-25 19:47:54,845 [nREPL-session-1e7253ab-43cc-406a-b42e-56d7442309f3] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-25 19:47:55,628 [nREPL-session-1e7253ab-43cc-406a-b42e-56d7442309f3] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-25 19:47:55,966 [nREPL-session-1e7253ab-43cc-406a-b42e-56d7442309f3] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-25 19:47:55,995 [nREPL-session-1e7253ab-43cc-406a-b42e-56d7442309f3] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-25 19:47:56,144 [nREPL-session-1e7253ab-43cc-406a-b42e-56d7442309f3] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-25 19:47:56,283 [nREPL-session-1e7253ab-43cc-406a-b42e-56d7442309f3] INFO  luminus.http-server - server started on port 3000 
2025-08-25 19:47:56,284 [nREPL-session-1e7253ab-43cc-406a-b42e-56d7442309f3] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
