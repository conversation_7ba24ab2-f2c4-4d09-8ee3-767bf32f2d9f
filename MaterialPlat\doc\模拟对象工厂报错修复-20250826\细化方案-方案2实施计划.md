# 方案2实施计划 - 重构模拟对象工厂设计

## 总体实施策略

采用**渐进式重构**策略，确保在重构过程中系统始终可用，降低风险。

## 详细实施阶段

### 阶段1：设计接口和抽象层（1天）

#### 1.1 创建核心接口（2小时）
**文件变更：**
- 新增：`MaterialPlat/TestHelper/TestHelper/Interfaces/ITemplateStore.cs`
- 新增：`MaterialPlat/TestHelper/TestHelper/Interfaces/IMockFactory.cs`
- 新增：`MaterialPlat/TestHelper/TestHelper/Interfaces/ITestServiceProvider.cs`

**具体任务：**
- [ ] 定义 `ITemplateStore` 接口，包含模板存储的基本操作
- [ ] 定义 `IMockFactory` 接口，抽象模拟对象创建逻辑
- [ ] 定义 `ITestServiceProvider` 接口，管理测试依赖注入

#### 1.2 设计依赖注入容器（2小时）
**文件变更：**
- 新增：`MaterialPlat/TestHelper/TestHelper/Services/TestServiceProvider.cs`
- 修改：`MaterialPlat/TestHelper/TestHelper.csproj` - 添加依赖注入包引用

**具体任务：**
- [ ] 设计轻量级的依赖注入容器
- [ ] 定义服务生命周期管理策略
- [ ] 实现服务注册和解析机制

#### 1.3 设计测试基类架构（2小时）
**文件变更：**
- 新增：`MaterialPlat/TestHelper/TestHelper/Base/BaseTest.cs`
- 新增：`MaterialPlat/TestHelper/TestHelper/Base/BaseTestWithMocks.cs`

**具体任务：**
- [ ] 设计测试基类层次结构
- [ ] 定义资源清理机制
- [ ] 设计测试隔离策略

#### 1.4 架构验证和调整（2小时）
**具体任务：**
- [ ] 创建概念验证测试
- [ ] 验证接口设计的合理性
- [ ] 根据验证结果调整设计

---

### 阶段2：实现核心组件（2-3天）

#### 2.1 实现模板存储服务（1天）
**文件变更：**
- 新增：`MaterialPlat/TestHelper/TestHelper/Services/TemplateStore.cs`
- 新增：`MaterialPlat/TestHelper/TestHelper/Services/ThreadSafeTemplateStore.cs`

**具体任务：**
- [ ] 实现基础的 `TemplateStore` 类
- [ ] 实现线程安全的模板存储
- [ ] 添加模板生命周期管理
- [ ] 实现自动清理机制
- [ ] 编写单元测试验证功能

#### 2.2 重构MockFactoryHelper（1天）
**文件变更：**
- 新增：`MaterialPlat/TestHelper/TestHelper/Factories/MockFactory.cs`
- 修改：`MaterialPlat/TestHelper/TestHelper/MockFactoryHelper.cs` - 标记为过时，保持向后兼容

**具体任务：**
- [ ] 创建新的实例化 `MockFactory` 类
- [ ] 实现依赖注入支持
- [ ] 添加自动资源管理
- [ ] 实现唯一模板名称生成策略
- [ ] 保持与原有API的兼容性
- [ ] 编写单元测试

#### 2.3 实现依赖注入容器（0.5天）
**文件变更：**
- 完善：`MaterialPlat/TestHelper/TestHelper/Services/TestServiceProvider.cs`

**具体任务：**
- [ ] 实现服务注册机制
- [ ] 实现服务解析和创建
- [ ] 添加生命周期管理
- [ ] 实现作用域管理
- [ ] 编写单元测试

#### 2.4 实现测试基础设施（0.5天）
**文件变更：**
- 完善：`MaterialPlat/TestHelper/TestHelper/Base/BaseTest.cs`
- 完善：`MaterialPlat/TestHelper/TestHelper/Base/BaseTestWithMocks.cs`

**具体任务：**
- [ ] 实现测试基类
- [ ] 添加自动依赖注入
- [ ] 实现资源自动清理
- [ ] 添加测试辅助方法
- [ ] 编写使用示例

---

### 阶段3：迁移现有测试（2天）

#### 3.1 迁移核心测试类（1天）
**文件变更：**
- 修改：`MaterialPlat/SubTaskList.Tests/Tasks/SubTaskDAQTests.cs`
- 修改：`MaterialPlat/SubTaskList.Tests/Tasks/SubTaskHighFreqJiaoBianDAQTests.cs`
- 修改：`MaterialPlat/SubTaskList.Tests/SubTaskCreepSignalCheckTests.cs`

**具体任务：**
- [ ] 更新测试类继承自新的基类
- [ ] 替换静态工厂调用为实例方法
- [ ] 更新模板名称生成策略
- [ ] 验证测试功能正确性
- [ ] 确保测试隔离有效

#### 3.2 迁移其他测试项目（0.5天）
**文件变更：**
- 修改：`MaterialPlat/ScriptEngine.Tests/` 中相关测试文件

**具体任务：**
- [ ] 识别所有使用MockFactoryHelper的测试
- [ ] 批量更新测试代码
- [ ] 验证迁移后的功能

#### 3.3 验证和测试（0.5天）
**具体任务：**
- [ ] 运行所有单元测试确保通过
- [ ] 进行并发测试验证线程安全
- [ ] 性能基准测试
- [ ] 内存泄漏检查

---

### 阶段4：清理和文档（1天）

#### 4.1 代码清理（0.5天）
**文件变更：**
- 修改：`MaterialPlat/TestHelper/TestHelper/MockFactoryHelper.cs` - 添加过时标记和迁移指南

**具体任务：**
- [ ] 标记旧代码为过时
- [ ] 添加迁移指南注释
- [ ] 清理未使用的代码
- [ ] 优化代码结构

#### 4.2 文档更新（0.5天）
**文件变更：**
- 新增：`MaterialPlat/TestHelper/README.md`
- 新增：`MaterialPlat/doc/模拟对象工厂报错修复-20250826/使用指南.md`
- 新增：`MaterialPlat/doc/模拟对象工厂报错修复-20250826/迁移指南.md`

**具体任务：**
- [ ] 编写新工厂的使用指南
- [ ] 创建迁移指南文档
- [ ] 更新项目README
- [ ] 添加代码示例

## 关键技术决策

### 1. 依赖注入框架选择
**决策：** 使用轻量级自实现容器，而非Microsoft.Extensions.DependencyInjection
**理由：** 
- 减少外部依赖
- 更好的控制和定制
- 避免与测试框架冲突

### 2. 向后兼容策略
**决策：** 保留原有API，标记为过时，提供迁移路径
**理由：**
- 降低迁移风险
- 允许渐进式迁移
- 给团队适应时间

### 3. 测试隔离策略
**决策：** 每个测试类使用独立的服务提供者实例
**理由：**
- 完全的测试隔离
- 避免测试间相互影响
- 更好的并发支持

## 风险控制措施

### 1. 分阶段验证
- 每个阶段完成后进行功能验证
- 保持可回滚的检查点
- 及时发现和解决问题

### 2. 并行开发
- 新旧系统并行运行
- 逐步切换，降低风险
- 保留应急回滚方案

### 3. 充分测试
- 单元测试覆盖所有新组件
- 集成测试验证整体功能
- 性能测试确保无回归

## 成功验收标准

### 功能验收
- [ ] 所有现有测试迁移完成并通过
- [ ] 新的依赖注入系统正常工作
- [ ] 测试隔离完全实现
- [ ] 不再出现重复键异常

### 性能验收
- [ ] 测试执行时间不超过原来的120%
- [ ] 内存使用合理，无明显泄漏
- [ ] 并发测试性能良好

### 质量验收
- [ ] 代码覆盖率不低于90%
- [ ] 通过所有代码审查
- [ ] 文档完整准确
- [ ] 符合编码规范

## 进度跟踪

**总预计工期：** 5-7天
**关键里程碑：**
- Day 1: 接口设计完成
- Day 2-4: 核心组件实现完成
- Day 5-6: 测试迁移完成
- Day 7: 清理和文档完成

**每日检查点：**
- 功能完成度检查
- 质量标准验证
- 风险评估更新
- 进度调整决策
