import React, {
    forwardRef, useCallback, useEffect, useImperativeHandle, useRef
} from 'react'
import {
    lightningChart,
    Themes,
    UILayoutBuilders,
    UIOrigins,
    UIDraggingModes,
    UIElementBuilders,
    SolidFill,
    ColorCSS
} from '@arction/lcjs'
import { useHotkeys } from 'react-hotkeys-hook'

import { initChartOption } from './utils/init'
import {
    moveCross, lineClickSyncCross, crossSwitchLine, openCross, setCrossPoint, moveCrossByPercent
} from './utils/lineCross'
import { initialOption } from './constants/option'
import { KEYBOARD_KEYS } from './constants/index'
import { PROPORTION_TYPE } from './constants/axis'
import { handleAutoExpand } from './utils/autoExpand'
import { Container } from './style'
import { convertLineData } from './utils/utils'
import {
    getAxisRanges,
    updateAllAuxiliaryLines,
    updateSingleAuxiliaryLine,
    drawAllAuxiliaryLines,
    drawAuxiliaryLine,
    getAuxiliaryLineStyle
} from './utils/auxiliaryLines'

import { initSingleChunk } from './utils/chunkMarker'

const ChartXY = ({
    option = initialOption,
    highlightLineId,
    crossPercent, // 十字线位置百分比
    onCrossMove = () => { }, // 十字线移动回调
    onAnnotationPositionChange = (a) => console.log('a', a), // 点标注位置变化回调
    onChunkMarkerPositionChange = (a) => console.log('a', a) // 块标注位置变化回调
}, ref) => {
    // DOM容器引用，用于挂载图表
    const containerDomRef = useRef()

    // 图表实例引用
    const chartRef = useRef(null)

    // X轴映射表引用，存储所有X轴实例
    const xAxisMapRef = useRef(null)

    // Y轴映射表引用，存储所有Y轴实例
    const yAxisMapRef = useRef(null)

    // 曲线映射表引用，存储所有曲线实例
    const lineMapRef = useRef(null)

    /**
     * @description 曲线缓存点的映射
     * 实在是找不到怎么从line实例上获取数据集的方法了 ai给的getPoints()是假的
     * 如果能直接从实力上获取当前线上所有点可以去掉 现在点的index可以直接加到点上
     * 但是键盘左右上下移动十字线拿不到目标点
     *
     * 现在这个历史数据的清空需要上层组件主动调用clearAllLine clearLine去清空
    */
    const lineDataMapRef = useRef(null)

    // 图例引用
    const legendRef = useRef(null)

    // 每个线上的十字线
    const lineCrossMapRef = useRef(null)

    // 当前开启十字线的线id
    const lineCrossIdRef = useRef(null)

    // 当前十字线所在点的下标 所在线所有数据集的下标  不是结果标签显示的索引
    const lineCrossIndexRef = useRef(null)

    // 结果标签 固定右上角 直接 setText('')
    const resultLabelRef = useRef({
        layout: null, // 容器
        titleElement: null,
        pointIndexText: null,
        pointXText: null,
        pointYText: null
    })

    // 点标注映射表引用，存储所有点标注配置信息
    const markerPointMapRef = useRef(null)

    // 点标注管理器引用，统一管理所有点标注
    const pointTagManagerRef = useRef(null)

    // 块标注
    const markerChunkMapRef = useRef(
        // {
        //     [id] = {
        //         id,
        //         lineId,
        //         chunkContainer,
        //         contentText
        //     }
        // }
    )

    // 辅助线映射表引用，存储所有辅助线实例
    const auxiliaryLinesMapRef = useRef(null)

    // 自动扩展标志位，控制是否应用自动扩展
    const autoExpandEnabledRef = useRef(true)

    // 轴监听器token存储，用于正确移除监听器
    const axisIntervalTokensRef = useRef({
        x: {}, // { [axisId]: token }
        y: {} // { [axisId]: token }
    })

    // 断裂点存储，格式：{ [lineId]: pointIndex }
    const breakPointRef = useRef({})

    // 十字线移动回调引用
    const onCrossMoveRef = useRef()

    useEffect(() => {
        onCrossMoveRef.current = onCrossMove
    }, [onCrossMove])

    const onPointMarkerPositionChangeRef = useRef()

    useEffect(() => {
        onPointMarkerPositionChangeRef.current = onAnnotationPositionChange
    }, [onAnnotationPositionChange])

    const onChunkMarkerPositionChangeRef = useRef()

    useEffect(() => {
        onChunkMarkerPositionChangeRef.current = onChunkMarkerPositionChange
    }, [onChunkMarkerPositionChange])

    /**
     * 创建初始化时的点标注
     * 检查 lineDataMapRef 中是否已有对应的数据点，如果有则创建标注
     */
    const createInitialAnnotations = () => {
        if (!markerPointMapRef.current || !pointTagManagerRef.current) {
            return
        }

        Object.entries(markerPointMapRef.current).forEach(([id, {
            pointIndex, lineId, title, lineInstance, isLine, isChunk, color, position
        }]) => {
            const lineData = convertLineData(lineMapRef.current[lineId], lineDataMapRef.current[lineId])
            if (!lineData || lineData.length === 0) {
                return
            }

            // 查找对应的数据点（根据 index 属性而非数组下标）
            const point = lineData.find(item => item.index === pointIndex)
            if (point) {
                // 创建标注
                pointTagManagerRef.current.createAnnotation({
                    id,
                    x: point.x,
                    y: point.y,
                    content: title,
                    lineInstance,
                    isLine,
                    isChunk,
                    color,
                    position,
                    style: {
                        offsetY: 1,
                        lineThickness: 1,
                        marginLeft: 8,
                        marginRight: 8,
                        marginTop: 4,
                        marginBottom: 4
                    }
                })
            }
        })
    }

    /**
     * 检查并创建新添加数据点对应的标注
     * @param {string} lineId - 线条ID
     * @param {Array} newLineData - 新添加的数据点
     */
    const createAnnotationsForNewData = (lineId, newLineData) => {
        if (!markerPointMapRef.current || !pointTagManagerRef.current) {
            return
        }

        // 查找该线条相关的点标注配置
        const lineMarkerPoints = Object.entries(markerPointMapRef.current)
            .filter(([, config]) => config.lineId === lineId)

        lineMarkerPoints.forEach(([id, {
            pointIndex, title, lineInstance, isLine, isChunk, color, position
        }]) => {
            // 检查新添加的数据中是否包含需要标注的点
            const targetPoint = newLineData.find(item => item.index === pointIndex)
            if (targetPoint) {
                // 创建标注
                pointTagManagerRef.current.createAnnotation({
                    id,
                    x: targetPoint.x,
                    y: targetPoint.y,
                    content: title,
                    lineInstance,
                    isLine,
                    isChunk,
                    color,
                    position,
                    style: {
                        offsetY: 1,
                        lineThickness: 1,
                        marginLeft: 8,
                        marginRight: 8,
                        marginTop: 4,
                        marginBottom: 4
                    }
                })
            }
        })
    }

    /**
     * 通过 useImperativeHandle 暴露给父组件的方法
     * 这些方法允许父组件直接操作图表，而不需要通过props传递
     */
    useImperativeHandle(ref, () => ({
        /**
         * 清除指定曲线的数据
         * @param {string} lineId - 曲线ID，对应配置中的曲线标识
         */
        clearLine: (lineId) => {
            if (!lineMapRef.current?.[lineId]) {
                console.warn('在不存在的曲线清除数据：', lineId)
                return
            }
            lineDataMapRef.current[lineId] = []
            lineMapRef.current?.[lineId]?.clear?.()
        },

        /**
         * 清除所有曲线的数据
         * 遍历所有曲线实例并调用其clear方法
         */
        clearAllLine: () => {
            Object.values(lineMapRef.current).forEach(line => {
                lineDataMapRef.current[line.id] = []
                line?.clear?.()
            })
        },
        /**
         * 向指定曲线添加数据点
         * @param {string} lineId - 曲线ID，对应配置中的曲线标识
         * @param {Array} lineData - 数据点数组，格式为 [{ x: number, y: number }, ...]
         */
        lineAdd: (lineId, lineData) => {
            if (!lineMapRef.current?.[lineId]) {
                console.warn('在为不存在的曲线添加点：', lineId)
                return
            }

            // 尝试添加index 二维数组没有index 静态曲线试验中没有index-历史曲线有指定的index
            const newLineData = lineData.map((i, idx) => {
                const index = lineDataMapRef.current[lineId].length + idx
                return {
                    index: lineDataMapRef.current[lineId].length + idx,
                    // 每搁十个点 放大特殊标记
                    size: index % 10 === 0 ? 5 : 1,
                    ...i
                }
            })

            // 将新数据添加到缓存中
            lineDataMapRef.current[lineId].push(...newLineData)

            // 获取当前线的断裂点
            const breakPointIndex = breakPointRef.current[lineId]

            // 判断线所在轴是否有扫描范围
            const xAxisInterval = option.xAxis.find(x => x.id === lineMapRef.current[lineId].xAxisId)?.interval
            const yAxisInterval = option.yAxis.find(y => y.id === lineMapRef.current[lineId].yAxisId)?.interval

            // 处理扫描范围
            if (
                xAxisInterval?.proportionType === PROPORTION_TYPE.扫描范围
                || yAxisInterval?.proportionType === PROPORTION_TYPE.扫描范围
            ) {
                const lastPoint = lineDataMapRef.current[lineId].at(-1)

                if (xAxisInterval.proportionType === PROPORTION_TYPE.扫描范围) {
                    // 先转单位
                    const lineDataUnit = convertLineData(lineMapRef.current[lineId], lineDataMapRef.current[lineId])
                    // 找到最后符合范围的点下标
                    const index = lineDataUnit.findIndex(item => item.x >= (lastPoint.x - xAxisInterval.lastRange))

                    lineDataMapRef.current[lineId] = lineDataMapRef.current[lineId].slice(index)
                }
                if (yAxisInterval.proportionType === PROPORTION_TYPE.扫描范围) {
                    // 先转单位
                    const lineDataUnit = convertLineData(lineMapRef.current[lineId], lineDataMapRef.current[lineId])
                    // 找到最后符合范围的点下标
                    const index = lineDataUnit.findIndex(item => item.y >= (lastPoint.y - yAxisInterval.lastRange))
                    // 截取数据
                    lineDataMapRef.current[lineId] = lineDataMapRef.current[lineId].slice(index)
                }

                lineMapRef.current?.[lineId].clear()

                lineMapRef.current?.[lineId]?.add?.(
                    convertLineData(lineMapRef.current[lineId], lineDataMapRef.current[lineId])
                )
            } else if (breakPointIndex) {
                // 判断断裂点
                // 确定要绘制的数据范围
                let dataToRender = lineDataMapRef.current[lineId]
                if (breakPointIndex !== undefined && breakPointIndex >= 0) {
                    // 如果有断裂点，只绘制断裂点之前的数据（包含断裂点）
                    dataToRender = lineDataMapRef.current[lineId].slice(0, breakPointIndex + 1)
                }

                if (dataToRender.length > 0) {
                    lineMapRef.current?.[lineId]?.add?.(
                        convertLineData(lineMapRef.current[lineId], dataToRender)
                    )
                }
            } else {
                lineMapRef.current?.[lineId]?.add?.(
                    convertLineData(lineMapRef.current[lineId], newLineData)
                )
            }

            // 检查并创建新添加数据点对应的标注
            createAnnotationsForNewData(lineId, newLineData)

            // 添加数据后自动扩展坐标轴范围（仅在启用自动扩展时）
            if (autoExpandEnabledRef.current) {
                executeAutoExpand()
            }
        },
        openCross: (lineId) => {
            // 优先使用传入的lineId，其次使用highlightLineId，最后使用第一个线的id
            const targetLineId = lineId ?? highlightLineId ?? Object.keys(lineMapRef.current)[0]

            openCross({
                targetLineId,
                // 这种直接穿ref的是需要修改值
                lineCrossIdRef,
                lineCrossIndexRef,
                // 这种取出current的是只使用值
                lineCrossMap: lineCrossMapRef.current,
                resultLabel: resultLabelRef.current,
                lineMap: lineMapRef.current,
                lineDataMap: lineDataMapRef.current,
                // 传递高亮处理函数
                onHighlightChange: (newLineId, oldLineId) => {
                    // 清除旧线的高亮
                    if (oldLineId && lineMapRef.current[oldLineId]) {
                        lineMapRef.current[oldLineId].setHighlightOnHover?.(false)
                        lineMapRef.current[oldLineId].setEffect?.(false)
                    }
                    // 设置新线的高亮
                    if (newLineId && lineMapRef.current[newLineId]) {
                        lineMapRef.current[newLineId].setHighlightOnHover?.(true)
                        lineMapRef.current[newLineId].setEffect?.(true)
                    }
                }
            })
        },
        closeCross: () => {
            // 隐藏十字线
            lineCrossMapRef.current[lineCrossIdRef.current]?.setVisible(false)

            // 清除当前十字线所在线的高亮
            if (lineCrossIdRef.current && lineMapRef.current[lineCrossIdRef.current]) {
                lineMapRef.current[lineCrossIdRef.current].setHighlightOnHover?.(false)
                lineMapRef.current[lineCrossIdRef.current].setEffect?.(false)
            }

            lineCrossIdRef.current = null

            // 隐藏结果标签
            resultLabelRef.current.layout.setVisible(false)

            // 恢复highlightLineId对应线的高亮
            if (highlightLineId && lineMapRef.current[highlightLineId]) {
                lineMapRef.current[highlightLineId].setHighlightOnHover?.(true)
                lineMapRef.current[highlightLineId].setEffect?.(true)
            }
        },
        // 获取当前选中的点
        getCrossPoint: () => {
            if (!lineCrossIdRef.current) {
                return null
            }

            const crossPoint = lineDataMapRef.current[lineCrossIdRef.current][lineCrossIndexRef.current]

            return crossPoint
        },
        // 显示标签
        showTag: () => {
            // 显示markerPoint - 使用统一的PointTagManager
            if (pointTagManagerRef.current) {
                pointTagManagerRef.current.showAllAnnotations()
            }

            // 显示标签块
            Object.values(markerChunkMapRef.current).forEach((i) => {
                i.chunkContainer.setVisible(true)
            })
        },
        hideTag: () => {
            // 隐藏markerPoint - 使用统一的PointTagManager
            if (pointTagManagerRef.current) {
                pointTagManagerRef.current.hideAllAnnotations()
            }

            Object.values(markerChunkMapRef.current).forEach((i) => {
                i.chunkContainer.setVisible(false)
            })
        },
        /**
         * 恢复图表到默认状态
         * 1. 将轴应用option中的默认范围
         * 2. 执行handleAutoExpand计算自动扩展后的范围
         * 3. 重新绘制辅助线
         * 4. 将标志位设置为true
         */
        restore: () => {
            // 恢复X轴默认范围
            Object.entries(xAxisMapRef.current || {}).forEach(([axisId, xAxis]) => {
                const axisConfig = (option.xAxis || []).find(axis => axis.id === axisId)
                if (xAxis && axisConfig && axisConfig.interval) {
                    xAxis.setInterval({ start: axisConfig.interval.start, end: axisConfig.interval.end })
                }
            })

            // 恢复Y轴默认范围
            Object.entries(yAxisMapRef.current || {}).forEach(([axisId, yAxis]) => {
                const axisConfig = (option.yAxis || []).find(axis => axis.id === axisId)
                if (yAxis && axisConfig && axisConfig.interval) {
                    yAxis.setInterval({ start: axisConfig.interval.start, end: axisConfig.interval.end })
                }
            })

            // 执行自动扩展
            executeAutoExpand()

            // 重新绘制所有辅助线（轴范围变化后需要重新计算辅助线位置）
            if (auxiliaryLinesMapRef.current && xAxisMapRef.current && yAxisMapRef.current) {
                drawAllAuxiliaryLines(
                    auxiliaryLinesMapRef.current,
                    xAxisMapRef.current,
                    yAxisMapRef.current,
                    lineDataMapRef.current
                )
            }

            // 重新启用自动扩展
            autoExpandEnabledRef.current = true
        },
        // 点标注管理方法
        /**
         * 显示指定标注
         * @param {string} id - 标注ID
         */
        showAnnotation: (id) => {
            if (pointTagManagerRef.current) {
                return pointTagManagerRef.current.showAnnotation(id)
            }
            return false
        },
        /**
         * 隐藏指定标注
         * @param {string} id - 标注ID
         */
        hideAnnotation: (id) => {
            if (pointTagManagerRef.current) {
                return pointTagManagerRef.current.hideAnnotation(id)
            }
            return false
        },
        /**
         * 切换所有标注的可见性
         */
        toggleAllAnnotations: () => {
            if (pointTagManagerRef.current) {
                pointTagManagerRef.current.toggleAllAnnotations()
            }
        },
        /**
         * 更新标注位置和内容
         * @param {string} id - 标注ID
         * @param {number} pointIndex - 新的数据点下标
         * @param {string} [content] - 可选的新标注内容
         */
        updateAnnotationPosition: (id, pointIndex, content, position) => {
            if (pointTagManagerRef.current) {
                // 从 markerPointMapRef 中找到对应的线条ID
                const markerPoint = markerPointMapRef.current[id]

                if (markerPoint) {
                    const lineData = convertLineData(lineMapRef.current[markerPoint.lineId], lineDataMapRef.current[markerPoint.lineId])

                    return pointTagManagerRef.current.updateAnnotationPosition(id, pointIndex, lineData, content, position)
                }
            }
            return false
        },
        /**
         * 获取标注数量
         */
        getAnnotationCount: () => {
            if (pointTagManagerRef.current) {
                return pointTagManagerRef.current.getAnnotationCount()
            }
            return 0
        },

        /**
         * 更新辅助线配置
         * @param {Array} newAuxiliaryConfig - 新的辅助线配置数组
         */
        updateAuxiliaryLines: (newAuxiliaryConfig) => {
            if (!newAuxiliaryConfig || !Array.isArray(newAuxiliaryConfig)) {
                console.warn('updateAuxiliaryLines: 无效的辅助线配置')
                return
            }

            if (auxiliaryLinesMapRef.current && xAxisMapRef.current && yAxisMapRef.current) {
                // 获取现有辅助线ID列表
                const existingIds = Object.keys(auxiliaryLinesMapRef.current)
                const newIds = newAuxiliaryConfig.map(config => config.id).filter(Boolean)

                // 删除不再需要的辅助线
                existingIds.forEach(id => {
                    if (!newIds.includes(id)) {
                        const auxiliaryLineData = auxiliaryLinesMapRef.current[id]
                        if (auxiliaryLineData && auxiliaryLineData.line) {
                            auxiliaryLineData.line.clear()
                            auxiliaryLineData.line.dispose()
                        }
                        delete auxiliaryLinesMapRef.current[id]
                    }
                })

                // 更新或创建辅助线
                newAuxiliaryConfig.forEach(config => {
                    if (config.id && config.configType) {
                        if (auxiliaryLinesMapRef.current[config.id]) {
                            // 更新现有辅助线
                            updateSingleAuxiliaryLine(
                                config.id,
                                auxiliaryLinesMapRef.current,
                                xAxisMapRef.current,
                                yAxisMapRef.current,
                                lineDataMapRef.current,
                                lineMapRef.current,
                                config
                            )
                        } else {
                            // 创建新的辅助线实例
                            const auxiliaryLine = chartRef.current.addLineSeries({
                                dataPattern: 'ProgressiveX'
                            })

                            auxiliaryLine.setCursorEnabled(false)
                            auxiliaryLine.setHighlightOnHover(false)
                            auxiliaryLine.setEffect(false)

                            // 设置辅助线样式
                            if (config.style) {
                                const lineStyle = getAuxiliaryLineStyle(config.style)
                                auxiliaryLine.setStrokeStyle(lineStyle)
                            }

                            // 存储辅助线数据
                            auxiliaryLinesMapRef.current[config.id] = {
                                line: auxiliaryLine,
                                config
                            }

                            // 绘制新辅助线
                            drawAuxiliaryLine(
                                auxiliaryLinesMapRef.current[config.id],
                                xAxisMapRef.current,
                                yAxisMapRef.current,
                                lineDataMapRef.current
                            )
                        }
                    }
                })
            }
        },

        /**
         * 更新单个辅助线
         * @param {string} auxiliaryLineId - 辅助线ID
         * @param {Object} newConfig - 新的辅助线配置
         */
        updateAuxiliaryLine: (auxiliaryLineId, newConfig) => {
            if (!auxiliaryLineId || !newConfig) {
                console.warn('updateAuxiliaryLine: 缺少必要参数')
                return false
            }

            if (auxiliaryLinesMapRef.current && xAxisMapRef.current && yAxisMapRef.current) {
                return updateSingleAuxiliaryLine(
                    auxiliaryLineId,
                    auxiliaryLinesMapRef.current,
                    xAxisMapRef.current,
                    yAxisMapRef.current,
                    lineDataMapRef.current,
                    lineMapRef.current,
                    newConfig
                )
            }
            return false
        },

        /**
         * 更新单个辅助线（别名方法，与 updateAuxiliaryLine 功能相同）
         * @param {string} auxiliaryLineId - 辅助线ID
         * @param {Object} newConfig - 新的辅助线配置
         */
        updateSingleAuxiliaryLine: (auxiliaryLineId, newConfig) => {
            if (!auxiliaryLineId || !newConfig) {
                console.warn('updateSingleAuxiliaryLine: 缺少必要参数')
                return false
            }

            if (auxiliaryLinesMapRef.current && xAxisMapRef.current && yAxisMapRef.current) {
                return updateSingleAuxiliaryLine(
                    auxiliaryLineId,
                    auxiliaryLinesMapRef.current,
                    xAxisMapRef.current,
                    yAxisMapRef.current,
                    lineDataMapRef.current,
                    lineMapRef.current,
                    newConfig
                )
            }
            return false
        },

        /**
         * 清除所有辅助线
         */
        clearAllAuxiliaryLines: () => {
            if (auxiliaryLinesMapRef.current) {
                Object.values(auxiliaryLinesMapRef.current).forEach(auxiliaryLineData => {
                    if (auxiliaryLineData.line) {
                        auxiliaryLineData.line.clear()
                    }
                })
            }
        },

        /**
         * 获取当前辅助线数量
         */
        getAuxiliaryLineCount: () => {
            return auxiliaryLinesMapRef.current ? Object.keys(auxiliaryLinesMapRef.current).length : 0
        },

        // 块标注管理方法
        /**
         * 更新指定块标注的内容
         * @param {string} id - 块标注ID
         * @param {Array<string>} content - 新的内容数组
         */
        updateChunkContent: ({
            id, showTitle, title, color, content, position
        }) => {
            const chunk = markerChunkMapRef.current[id]
            if (chunk && chunk.chunkContainer) {
                // 保存原有的状态信息
                const originalVisible = chunk.chunkContainer.getVisible()
                const originalPosition = chunk.chunkContainer.getPosition()

                // 清除容器中现有的所有元素
                chunk.chunkContainer.dispose()

                const newChunkContainer = initSingleChunk({
                    chartXY: chartRef.current, id, showTitle, title, color, content, position, onChunkMarkerPositionChangeRef
                })

                newChunkContainer.setVisible(originalVisible)

                // 更新引用
                chunk.chunkContainer = newChunkContainer

                return true
            }
            console.warn(`块标注 ${id} 不存在`)
            return false
        },

        /**
         * 显示指定块标注
         * @param {string} id - 块标注ID
         */
        showChunk: (id) => {
            const chunk = markerChunkMapRef.current[id]
            if (chunk && chunk.chunkContainer) {
                chunk.chunkContainer.setVisible(true)
                return true
            }
            console.warn(`块标注 ${id} 不存在`)
            return false
        },

        /**
         * 隐藏指定块标注
         * @param {string} id - 块标注ID
         */
        hideChunk: (id) => {
            const chunk = markerChunkMapRef.current[id]
            if (chunk && chunk.chunkContainer) {
                chunk.chunkContainer.setVisible(false)
                return true
            }
            console.warn(`块标注 ${id} 不存在`)
            return false
        },

        /**
         * 切换指定块标注的可见性
         * @param {string} id - 块标注ID
         */
        toggleChunk: (id) => {
            const chunk = markerChunkMapRef.current[id]
            if (chunk && chunk.chunkContainer) {
                const isVisible = chunk.chunkContainer.getVisible()
                chunk.chunkContainer.setVisible(!isVisible)
                return !isVisible
            }
            console.warn(`块标注 ${id} 不存在`)
            return false
        },

        /**
         * 获取块标注数量
         */
        getChunkCount: () => {
            return markerChunkMapRef.current ? Object.keys(markerChunkMapRef.current).length : 0
        },
        /**
         * 设置断裂点
         * 设置当前十字线所在线的选中点为断裂点（没开启十字线则不处理）
         */
        setBreakPoint: () => {
            // 检查是否有十字线开启
            if (!lineCrossIdRef.current || lineCrossIndexRef.current === null) {
                console.warn('没有开启十字线或未选中点，无法设置断裂点')
                return
            }

            const lineId = lineCrossIdRef.current
            const pointIndexValue = lineCrossIndexRef.current // 这是点对象的index属性值
            const lineData = lineDataMapRef.current[lineId]

            if (!lineData || lineData.length === 0) {
                console.warn('线数据为空，无法设置断裂点')
                return
            }

            // 根据点的index属性值找到对应的数组下标
            const arrayIndex = lineData.findIndex(point => point.index === pointIndexValue)

            if (arrayIndex === -1) {
                console.warn(`无法找到index为${pointIndexValue}的点，无法设置断裂点`)
                return
            }

            // 设置断裂点（存储数组下标）
            breakPointRef.current[lineId] = arrayIndex

            // 重新绘制该线，只显示断裂点之前的数据（包含断裂点）
            const dataToRender = lineData.slice(0, arrayIndex + 1)
            lineMapRef.current?.[lineId]?.clear?.()
            if (dataToRender.length > 0) {
                lineMapRef.current?.[lineId]?.add?.(
                    convertLineData(lineMapRef.current[lineId], dataToRender)
                )
            }

            console.log(`已设置断裂点：线 ${lineId}，点index ${pointIndexValue}，数组下标 ${arrayIndex}`)
        },
        /**
         * 撤销断裂点
         * 清空所有的断裂点并将所有曲线数据重新绘制到曲线上
         */
        clearBreakPoint: () => {
            // 清空所有断裂点
            breakPointRef.current = {}

            // 重新绘制所有曲线的完整数据
            Object.entries(lineDataMapRef.current || {}).forEach(([lineId, lineData]) => {
                if (lineData && lineData.length > 0) {
                    lineMapRef.current?.[lineId]?.clear?.()
                    lineMapRef.current?.[lineId]?.add?.(
                        convertLineData(lineMapRef.current[lineId], lineData)
                    )
                }
            })

            console.log('已清除所有断裂点，重新绘制完整曲线数据')
            // 调用恢复
            ref.current?.restore?.()
        }
    }))

    useHotkeys(Object.values(KEYBOARD_KEYS.移动十字线), (e) => {
        if (lineCrossIdRef.current) {
            moveCross({
                key: e.key,
                lineData: convertLineData(lineMapRef.current[lineCrossIdRef.current], lineDataMapRef.current[lineCrossIdRef.current]),
                line: lineMapRef.current[lineCrossIdRef.current],
                lineCross: lineCrossMapRef.current[lineCrossIdRef.current],
                resultLabel: resultLabelRef.current,
                lineCrossIndexRef,
                onCrossMoveRef,
                breakPointIndex: breakPointRef.current[lineCrossIdRef.current]
            })
        }
    })

    useHotkeys(Object.values(KEYBOARD_KEYS.切换线), (e) => {
        if (lineCrossIdRef.current) {
            crossSwitchLine({
                key: e.key,
                lineCrossIdRef,
                lineCrossIndexRef,
                lineCrossMap: lineCrossMapRef.current,
                resultLabel: resultLabelRef.current,
                lineMap: lineMapRef.current,
                lineDataMap: lineDataMapRef.current,
                breakPointRef,
                // 传递高亮处理函数
                onHighlightChange: (newLineId, oldLineId) => {
                    // 清除旧线的高亮
                    if (oldLineId && lineMapRef.current[oldLineId]) {
                        lineMapRef.current[oldLineId].setHighlightOnHover?.(false)
                        lineMapRef.current[oldLineId].setEffect?.(false)
                    }
                    // 设置新线的高亮
                    if (newLineId && lineMapRef.current[newLineId]) {
                        lineMapRef.current[newLineId].setHighlightOnHover?.(true)
                        lineMapRef.current[newLineId].setEffect?.(true)
                    }
                }
            })
        }
    })

    /**
     * 监听crossPercent变化，同步移动十字线位置
     * 当crossPercent发生变化时，将十字线移动到对应百分比的位置
     */
    useEffect(() => {
        moveCrossByPercent({
            crossPercent,
            lineCrossIdRef,
            lineDataMapRef,
            lineMapRef,
            lineCrossMapRef,
            resultLabelRef,
            lineCrossIndexRef,
            breakPointRef
        })
    }, [crossPercent])

    /**
     * 监听配置项变化，重新初始化图表
     * 当option发生变化时，会销毁旧图表并创建新图表
     */
    useEffect(() => {
        console.log('ChartXY - option', option)

        // option变化时重置自动扩展标志位为true
        autoExpandEnabledRef.current = true

        // 初始化图表
        initChart()

        // 点击线更新十字线位置
        lineClickSyncCross({
            lineMap: lineMapRef.current,
            lineCrossMap: lineCrossMapRef.current,
            resultLabel: resultLabelRef.current,
            lineCrossIndexRef,
            lineDataMapRef,
            onCrossMoveRef
        })

        // 清理函数：组件卸载或option变化时执行
        return () => {
            // 清理PointTagManager资源
            if (pointTagManagerRef.current) {
                pointTagManagerRef.current.dispose()
            }

            // 销毁图表实例，释放内存
            chartRef.current?.dispose()

            // 清空所有引用
            chartRef.current = null
            xAxisMapRef.current = null
            yAxisMapRef.current = null
            lineMapRef.current = null
            legendRef.current = null
            resultLabelRef.current = null
            lineCrossMapRef.current = null
            markerPointMapRef.current = null
            markerChunkMapRef.current = null
            pointTagManagerRef.current = null
            auxiliaryLinesMapRef.current = null

            // 重置轴监听器token存储
            axisIntervalTokensRef.current = { x: {}, y: {} }

            // 清空断裂点存储
            breakPointRef.current = {}
        }
    }, [option])

    /**
     * 监听highlightLineId变化，设置对应线的高亮
     * 当highlightLineId发生变化时，高亮指定的线条
     */
    useEffect(() => {
        if (!lineMapRef.current) {
            return
        }

        // 清除所有线条的高亮
        Object.values(lineMapRef.current).forEach(line => {
            line?.setEffect?.(false)
        })

        // 设置指定线条的高亮
        if (highlightLineId && lineMapRef.current[highlightLineId]) {
            lineMapRef.current[highlightLineId].setEffect?.(true)
        }
    }, [highlightLineId])

    // 监听用户手动操作图表（缩放、平移等），禁用自动扩展
    const handleUserInteraction = useCallback(() => {
        autoExpandEnabledRef.current = false
    }, [])

    /**
     * 安全执行自动扩展，避免触发用户交互事件
     */
    const executeAutoExpand = () => {
        // 移除X轴监听器
        Object.entries(xAxisMapRef.current || {}).forEach(([axisId, xAxis]) => {
            const token = axisIntervalTokensRef.current.x[axisId]
            if (token) {
                xAxis.offIntervalChange(token)
            }
        })

        // 移除Y轴监听器
        Object.entries(yAxisMapRef.current || {}).forEach(([axisId, yAxis]) => {
            const token = axisIntervalTokensRef.current.y[axisId]
            if (token) {
                yAxis.offIntervalChange(token)
            }
        })

        // 执行自动扩展
        handleAutoExpand({
            option, xAxisMapRef, yAxisMapRef, lineMapRef
        })

        // 重新添加X轴监听器并保存token
        Object.entries(xAxisMapRef.current || {}).forEach(([axisId, xAxis]) => {
            const token = xAxis.onIntervalChange(() => {
                handleUserInteraction()
                // 轴范围变化时更新辅助线
                if (auxiliaryLinesMapRef.current) {
                    updateAllAuxiliaryLines(auxiliaryLinesMapRef.current, xAxisMapRef.current, yAxisMapRef.current, lineDataMapRef.current)
                }
            })
            axisIntervalTokensRef.current.x[axisId] = token
        })

        // 重新添加Y轴监听器并保存token
        Object.entries(yAxisMapRef.current || {}).forEach(([axisId, yAxis]) => {
            const token = yAxis.onIntervalChange(() => {
                handleUserInteraction()
                // 轴范围变化时更新辅助线
                if (auxiliaryLinesMapRef.current) {
                    updateAllAuxiliaryLines(auxiliaryLinesMapRef.current, xAxisMapRef.current, yAxisMapRef.current, lineDataMapRef.current)
                }
            })
            axisIntervalTokensRef.current.y[axisId] = token
        })
    }

    /**
     * 初始化图表实例
     * 创建LightningChart图表实例，并根据配置项初始化坐标轴、曲线、图例等组件
     */
    const initChart = () => {
        // 创建LightningChart XY图表实例
        const chartXY = lightningChart().ChartXY({
            container: containerDomRef.current, // 挂载到DOM容器
            theme: Themes.light // 使用浅色主题
        })

        // 关闭鼠标交互自适应曲线
        chartXY.setMouseInteractionRectangleFit(false)

        // 根据配置项初始化图表选项，返回各组件的映射表
        const {
            xAxisMap, // X轴映射表
            yAxisMap, // Y轴映射表
            linesMap, // 曲线映射表
            legend, // 图例实例
            resultLabel,
            lineCrossMap,
            markerPointMap,
            markerChunkMap,
            pointTagManager, // 点标注管理器
            auxiliaryLinesMap // 辅助线映射表
        } = initChartOption(
            chartXY,
            option,
            onPointMarkerPositionChangeRef,
            onChunkMarkerPositionChangeRef
        )

        // 保存各组件实例到ref中，供其他方法使用
        chartRef.current = chartXY
        xAxisMapRef.current = xAxisMap
        yAxisMapRef.current = yAxisMap
        lineMapRef.current = linesMap
        legendRef.current = legend
        resultLabelRef.current = resultLabel
        lineCrossMapRef.current = lineCrossMap
        markerPointMapRef.current = markerPointMap
        markerChunkMapRef.current = markerChunkMap
        pointTagManagerRef.current = pointTagManager
        auxiliaryLinesMapRef.current = auxiliaryLinesMap

        // 曲线数据等外层组件清空 内部不再清空
        lineDataMapRef.current = Object.fromEntries(
            Object.keys(linesMap).map(id => ([id, lineDataMapRef.current?.[id] ?? []]))
        )

        // 初始化断裂点配置
        breakPointRef.current = { ...option.breakPoint } || {}

        // 渲染当前数据，考虑断裂点
        Object.entries(lineDataMapRef.current).forEach(([id, lineData]) => {
            if (lineData.length !== 0) {
                // 获取当前线的断裂点
                const breakPointIndex = breakPointRef.current[id]

                if (breakPointIndex) {
                    // 确定要绘制的数据范围
                    let dataToRender = lineData
                    if (breakPointIndex !== undefined && breakPointIndex >= 0) {
                        // 如果有断裂点，只绘制断裂点之前的数据（包含断裂点）
                        dataToRender = lineData.slice(0, breakPointIndex + 1)
                    }

                    if (dataToRender.length > 0) {
                        lineMapRef.current[id].add(convertLineData(lineMapRef.current[id], dataToRender))
                    }
                } else {
                    lineMapRef.current[id].add(convertLineData(lineMapRef.current[id], lineData))
                }
            }
        })

        // 初始化执行一次自动扩展
        handleAutoExpand({
            option, xAxisMapRef, yAxisMapRef, lineMapRef
        })

        // 初始化绘制辅助线
        if (auxiliaryLinesMap && Object.keys(auxiliaryLinesMap).length > 0) {
            drawAllAuxiliaryLines(
                auxiliaryLinesMap,
                xAxisMap,
                yAxisMap,
                lineDataMapRef.current
            )
        }

        // 为所有X轴添加用户交互监听并保存token
        Object.entries(xAxisMap).forEach(([axisId, xAxis]) => {
            const token = xAxis.onIntervalChange(() => {
                handleUserInteraction()
                // 轴范围变化时更新辅助线
                if (auxiliaryLinesMapRef.current) {
                    updateAllAuxiliaryLines(auxiliaryLinesMapRef.current, xAxisMapRef.current, yAxisMapRef.current, lineDataMapRef.current)
                }
            })
            axisIntervalTokensRef.current.x[axisId] = token
        })

        // 为所有Y轴添加用户交互监听并保存token
        Object.entries(yAxisMap).forEach(([axisId, yAxis]) => {
            const token = yAxis.onIntervalChange(() => {
                handleUserInteraction()
                // 轴范围变化时更新辅助线
                if (auxiliaryLinesMapRef.current) {
                    updateAllAuxiliaryLines(auxiliaryLinesMapRef.current, xAxisMapRef.current, yAxisMapRef.current, lineDataMapRef.current)
                }
            })
            axisIntervalTokensRef.current.y[axisId] = token
        })

        // 初始化时检查是否有点标注需要创建
        createInitialAnnotations()

        // 初始化时绘制辅助线
        if (auxiliaryLinesMapRef.current && Object.keys(auxiliaryLinesMapRef.current).length > 0) {
            drawAllAuxiliaryLines(auxiliaryLinesMapRef.current, xAxisMapRef.current, yAxisMapRef.current, lineDataMapRef.current)
        }
    }

    // 渲染图表组件
    return (
        <Container>
            {/* 图表容器：LightningChart将在此DOM元素中渲染图表 */}
            <div
                ref={containerDomRef}
                style={{ width: '100%', height: '100%' }}
            />
        </Container>
    )
}

export default forwardRef(ChartXY)
