import {
    SOURCE_TYPE, LINE_STYLE_TYPE, PROPORTION_TYPE, INTERVAL_TYPE
} from './constants'

export const initialOption = {
    base: {
        isName: true,
        name: '曲线',
        sourceType: SOURCE_TYPE.单数据源,
        sourceInputCode: '',
        updateFreq: 180,
        crossInputCode: '',
        xOffset: 0,
        yOffset: 0
    },
    curveGroup: {
        yAxis: {
            isEnable: true,
            index: 0,
            name: 'Y1轴曲线组',
            xSignal: '',
            xUnit: '',
            ySignal: [],
            curves: {}
        },
        y2Axis: {
            isEnable: false,
            index: 1,
            name: 'Y2轴曲线组',
            xSignal: '',
            xUnit: '',
            ySignal: [],
            curves: {}
        }
    },
    xAxis: {
        name: 'x轴',
        unit: '',
        proportionType: 'not',
        lowLimit: 0,
        upLimit: 10,
        lastRange: 10,
        isLog: false,
        type: 'solid',
        thickness: 2,
        color: '#000000',
        isGrid: false,
        gridType: 'solid',
        gridThickness: 1,
        gridColor: '#000000',
        isZeroLine: false,
        zeroLineType: 'solid',
        zeroLineThickness: 1,
        zeroLineColor: '#000000'
    },
    yAxis: {
        name: 'y轴',
        proportionType: 'not',
        lowLimit: 0,
        upLimit: 10,
        lastRange: 10,
        isLog: false,
        type: 'solid',
        thickness: 2,
        color: '#000000',
        isGrid: false,
        gridType: 'solid',
        gridThickness: 1,
        gridColor: '#000000',
        isZeroLine: false,
        zeroLineType: 'solid',
        zeroLineThickness: 1,
        zeroLineColor: '#000000'
    },
    y2Axis: {
        name: 'y2轴',
        proportionType: 'not',
        lowLimit: 0,
        upLimit: 10,
        lastRange: 10,
        isLog: false,
        type: 'solid',
        thickness: 2,
        color: '#000000',
        isGrid: false,
        gridType: 'solid',
        gridThickness: 1,
        gridColor: '#000000',
        isZeroLine: false,
        zeroLineType: 'solid',
        zeroLineThickness: 1,
        zeroLineColor: '#000000'
    },
    auxiliary: [],
    legend: {
        open: false
    },
    pointTag: {
        open: false
    },
    chunkTag: {
        open: true,
        list: []
    },
    tag: {
        isChunkTag: false,
        chunkTags: []
    },
    breakPoint: {},
    marker: [],
    defineAxis: {
        isDefineAxis: false,
        inputCode: '',
        source: []
    }
}
