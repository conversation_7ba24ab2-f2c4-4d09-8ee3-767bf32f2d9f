import { v4 as uuidv4 } from 'uuid'

import store from '@/redux/store/index'

import { SOURCE_TYPE } from '../constants/constants'
import { getColumnsSource } from './geColumnsSource'

const unitCodeToId = (code) => {
    let id = ''
    store.getState().global.unitList.forEach((item) => {
        if (item.units.find(f => f.code === code)) {
            id = item.units.find(f => f.code === code).id
        }
    })

    return id
}

/**
 * 将下划线命名转换为驼峰命名
 * @param {string} str - 下划线命名的字符串
 * @returns {string} 驼峰命名的字符串
 */
function toCamelCase(str) {
    return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase())
}

/**
 * 递归转换对象的键名从下划线到驼峰
 * @param {any} obj - 要转换的对象
 * @returns {any} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
    if (obj === null || obj === undefined) {
        return obj
    }

    if (Array.isArray(obj)) {
        return obj.map(item => convertKeysToCamelCase(item))
    }

    if (typeof obj === 'object') {
        const converted = {}
        // eslint-disable-next-line no-restricted-syntax
        for (const [key, value] of Object.entries(obj)) {
            const camelKey = toCamelCase(key)
            converted[camelKey] = convertKeysToCamelCase(value)
        }
        return converted
    }

    return obj
}

/**
 * 将老的配置结构转换为新的配置结构
 * @param {Object} oldConfig - 老的配置对象
 * @returns {Object} 新的配置对象
 */
function convertOldConfigToNew(oldConfig) {
    // 首先进行驼峰转换
    const camelCaseConfig = convertKeysToCamelCase(oldConfig)

    const sourceType = camelCaseConfig.base?.sourceType === 'array' ? SOURCE_TYPE.单数据源 : SOURCE_TYPE.多数据源
    const sourceInputCode = camelCaseConfig.base?.sourceInputCode || ''

    const channels = getColumnsSource({ sourceType, sourceInputCode })

    // 构建新的配置结构
    const newConfig = {
        base: {
            isName: camelCaseConfig.base?.isName || false,
            name: camelCaseConfig.base?.name || '',
            sourceType,
            sourceInputCode,
            updateFreq: camelCaseConfig.base?.updateFreq || 180,
            crossInputCode: camelCaseConfig.base?.crossInputCode || ''
        },
        curveGroup: {
            yAxis: {
                isEnable: false,
                index: 0,
                name: 'Y1轴曲线组',
                xSignal: '',
                xUnit: '',
                ySignal: [],
                curves: { }
            },
            y2Axis: {
                isEnable: false,
                index: 1,
                name: 'Y2轴曲线组',
                xSignal: 'time',
                xUnit: '',
                ySignal: [],
                curves: { }
            }
        },
        xAxis: {
            name: camelCaseConfig.xAxis?.name || '',
            unit: unitCodeToId(camelCaseConfig.xAxis?.unit) || '',
            proportionType: camelCaseConfig.xAxis?.proportionType || 'not',
            lowLimit: camelCaseConfig.xAxis?.lowLimit || 0,
            upLimit: camelCaseConfig.xAxis?.upLimit || 10,
            lastRange: camelCaseConfig.xAxis?.lastRange || 10,
            isLog: camelCaseConfig.xAxis?.intervalType === 'log',
            type: camelCaseConfig.xAxis?.type || 'solid',
            thickness: camelCaseConfig.xAxis?.thickness || 2,
            color: camelCaseConfig.xAxis?.color || '#000000',
            isGrid: camelCaseConfig.xAxis?.isGrid || false,
            gridType: camelCaseConfig.xAxis?.gridType || 'solid',
            gridThickness: camelCaseConfig.xAxis?.gridThickness || 1,
            gridColor: camelCaseConfig.xAxis?.gridColor || '#000000',
            isZeroLine: camelCaseConfig.xAxis?.isZeroLine || false,
            zeroLineType: camelCaseConfig.xAxis?.zeroLineType || 'solid',
            zeroLineThickness: camelCaseConfig.xAxis?.zeroLineThickness || 1,
            zeroLineColor: camelCaseConfig.xAxis?.zeroLineColor || '#000000'
        },
        yAxis: {
            name: camelCaseConfig.yAxis?.name || '',
            proportionType: camelCaseConfig.yAxis?.proportionType || 'not',
            lowLimit: camelCaseConfig.yAxis?.lowLimit || 0,
            upLimit: camelCaseConfig.yAxis?.upLimit || 10,
            lastRange: camelCaseConfig.yAxis?.lastRange || 10,
            isLog: camelCaseConfig.yAxis?.intervalType === 'log',
            type: camelCaseConfig.yAxis?.type || 'solid',
            thickness: camelCaseConfig.yAxis?.thickness || 2,
            color: camelCaseConfig.yAxis?.color || '#000000',
            isGrid: camelCaseConfig.yAxis?.isGrid || false,
            gridType: camelCaseConfig.yAxis?.gridType || 'solid',
            gridThickness: camelCaseConfig.yAxis?.gridThickness || 1,
            gridColor: camelCaseConfig.yAxis?.gridColor || '#000000',
            isZeroLine: camelCaseConfig.yAxis?.isZeroLine || false,
            zeroLineType: camelCaseConfig.yAxis?.zeroLineType || 'solid',
            zeroLineThickness: camelCaseConfig.yAxis?.zeroLineThickness || 1,
            zeroLineColor: camelCaseConfig.yAxis?.zeroLineColor || '#000000'
        },
        y2Axis: {
            name: camelCaseConfig.y2Axis?.name || '',
            proportionType: camelCaseConfig.y2Axis?.proportionType || 'not',
            lowLimit: camelCaseConfig.y2Axis?.lowLimit || 0,
            upLimit: camelCaseConfig.y2Axis?.upLimit || 10,
            lastRange: camelCaseConfig.y2Axis?.lastRange || 10,
            isLog: camelCaseConfig.y2Axis?.intervalType === 'log',
            type: camelCaseConfig.y2Axis?.type || 'solid',
            thickness: camelCaseConfig.y2Axis?.thickness || 2,
            color: camelCaseConfig.y2Axis?.color || '#000000',
            isGrid: camelCaseConfig.y2Axis?.isGrid || false,
            gridType: camelCaseConfig.y2Axis?.gridType || 'solid',
            gridThickness: camelCaseConfig.y2Axis?.gridThickness || 1,
            gridColor: camelCaseConfig.y2Axis?.gridColor || '#000000',
            isZeroLine: camelCaseConfig.y2Axis?.isZeroLine || false,
            zeroLineType: camelCaseConfig.y2Axis?.zeroLineType || 'solid',
            zeroLineThickness: camelCaseConfig.y2Axis?.zeroLineThickness || 1,
            zeroLineColor: camelCaseConfig.y2Axis?.zeroLineColor || '#000000'
        },
        auxiliary: camelCaseConfig.auxiliary || [],
        legend: {
            open: camelCaseConfig.tag?.isLegend || true
        },
        pointTag: {
            open: camelCaseConfig.tag?.isTag || false
        },
        chunkTag: {
            open: camelCaseConfig.tag?.isChunkTag || false,
            list: camelCaseConfig.tag?.chunkTags || []
        },
        tag: {
            isChunkTag: camelCaseConfig.tag?.isChunkTag || false,
            chunkTags: camelCaseConfig.tag?.chunkTags || []
        },
        breakPoint: {},
        marker: camelCaseConfig.marker || [],
        defineAxis: {
            isDefineAxis: camelCaseConfig.defineAxis?.isDefineAxis || false,
            inputCode: camelCaseConfig.defineAxis?.inputCode || '',
            source: camelCaseConfig.defineAxis?.source || []
        }
    }

    // 处理curve数组转换为curveGroup
    if (camelCaseConfig.curve && Array.isArray(camelCaseConfig.curve)) {
        camelCaseConfig.curve.forEach((curveItem, index) => {
            if (index === 0) {
                // Y1轴曲线组
                newConfig.curveGroup.yAxis = {
                    isEnable: curveItem.isEnable || false,
                    name: curveItem.name || 'Y1轴曲线组',
                    xSignal: curveItem.xAxis || '',
                    xUnit: '',
                    ySignal: curveItem.yAxis || [],
                    curves: convertCurveStyles(curveItem.styles || [[]], curveItem.name, camelCaseConfig.yAxis.unit, channels)
                }
            } else if (index === 1) {
                // Y2轴曲线组
                newConfig.curveGroup.y2Axis = {
                    isEnable: curveItem.isEnable || false,
                    index: curveItem.index || 1,
                    name: curveItem.name || 'Y2轴曲线组',
                    xSignal: curveItem.xAxis || '',
                    xUnit: '',
                    ySignal: curveItem.yAxis || [],
                    curves: convertCurveStyles(curveItem.styles || [[]], curveItem.name, camelCaseConfig.y2Axis.unit, channels)
                }
            }
        })
    }

    return newConfig
}

/**
 * 转换曲线样式数组
 * @param {Array} styles - 老的样式数组
 * @returns {Object} 新的曲线对象
 */
function convertCurveStyles(styles, name, unit, channels) {
    if (!Array.isArray(styles)) {
        return { }
    }

    const result = {}
    styles.forEach((styleGroup, i) => {
        if (!Array.isArray(styleGroup)) {
            result[i] = { lines: [] }
            return
        }

        result[i] = {
            lines: styleGroup.map((style, j) => ({
                isLine: style.isLine || true,
                lineType: style.lineType || 'solid',
                lineThickness: style.lineThickness || 1,
                isSign: style.isSign || false,
                signType: style.signType || 'o',
                signEach: style.signEach || false,
                color: style.color === 'transparent' ? '#000000' : (style.color || '#000000'),
                code: style.code || '',
                isApply: style.isApply || false,
                yUnit: unitCodeToId(unit),
                id: style.id || uuidv4(),
                name: style.name || `${name}-二维数组[${i}]-${channels.find(item => item.code === style.code)?.name ?? style.code}`,
                pointTags: []
            }))
        }
    })

    return result
}

// 导出函数
export {
    convertOldConfigToNew
}
