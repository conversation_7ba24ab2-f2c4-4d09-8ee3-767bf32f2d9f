{"version": 2, "dgSpecHash": "uFSmET+4UgA=", "success": true, "projectFilePath": "D:\\WorkProject\\ZJ\\MaterialPlat\\HardwareSim\\IHardware\\IHardware.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\messagepack\\3.1.3\\messagepack.3.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack.annotations\\3.1.3\\messagepack.annotations.3.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepackanalyzer\\3.1.3\\messagepackanalyzer.3.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.stringtools\\17.11.4\\microsoft.net.stringtools.17.11.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\8.0.0\\system.collections.immutable.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"], "logs": []}