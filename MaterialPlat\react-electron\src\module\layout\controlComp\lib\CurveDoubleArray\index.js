import React, {
    useRef, useState, useEffect, useCallback, useMemo
} from 'react'
import { useSelector } from 'react-redux'
import isEqual from 'lodash/isEqual'

import { findItem } from '@/utils/utils'
import useWidget from '@/hooks/useWidget'

import { initialOption } from './constants/initialOption'
import { convertOldConfigToNew } from './arrayUtils/convertOldConfigToNew'

import CompRender from './CompRender'

const CurveDoubleArray = ({
    item, id, layoutConfig,
    isFission, isRightClick
}) => {
    // 老曲线配置
    const currentSettingIdFromWidget = item?.widget_data_source ? JSON.parse(item?.widget_data_source) : null
    const arrayCurveConfig = useSelector(useCallback(state => {
        return state.template.arrayCurveConfigList.find(f => f.curve_id === currentSettingIdFromWidget)
    }, [currentSettingIdFromWidget]))

    // 新曲线配置
    const widgetData = useSelector(state => state.template.widgetData)
    const widget = useMemo(() => findItem(widgetData, 'widget_id', item?.widget_id), [item, widgetData])
    const { editWidget } = useWidget()

    const [config, setConfig] = useState()

    useEffect(() => {
        // 判断有没有新配置
        if (widget?.data_source && widget?.data_source?.curveGroup) {
            // 同步配置
            if (!isEqual(config, widget?.data_source)) {
                setConfig(widget?.data_source)
            }

            return
        }

        // 没有新配置则转换老配置
        if (arrayCurveConfig) {
            const newConfig = convertOldConfigToNew(arrayCurveConfig)
            console.log('配置', arrayCurveConfig, newConfig)
            // 更新
            updateConfig(newConfig)
            return
        }

        // 啥都没有则应用默认配置
        setConfig(initialOption)
    }, [widget, arrayCurveConfig])

    // 新配置 更新持久化配置
    const updateConfig = (newConfig) => {
        setConfig(newConfig)

        editWidget({
            ...widget,
            data_source: newConfig
        })
    }

    // 区分配置和状态
    const compConfigCacheRef = useRef()
    const compConfig = useMemo(() => {
        if (!config) {
            return null
        }

        const { compStatus, ...c } = config

        if (isEqual(compConfigCacheRef.current, c)) {
            return compConfigCacheRef.current
        }

        compConfigCacheRef.current = c

        return c
    }, [config])

    const compStatusCacheRef = useRef()
    const compStatus = useMemo(() => {
        if (!config) {
            return null
        }
        const { compStatus: s } = config

        if (isEqual(compStatusCacheRef.current, s)) {
            return compStatusCacheRef.current
        }

        compStatusCacheRef.current = s

        return s
    }, [config])

    return (
        <CompRender
            id={id}
            layoutConfig={layoutConfig}
            config={compConfig}
            compStatus={compStatus}
            updateConfig={updateConfig}
            isBufferCurve={false}
        />
    )
}

export default CurveDoubleArray
