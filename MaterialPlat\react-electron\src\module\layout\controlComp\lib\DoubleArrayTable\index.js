import React, { useState, useMemo, useRef } from 'react'
import { useSelector } from 'react-redux'
import isEqual from 'lodash/isEqual'

import useWidget from '@/hooks/useWidget'
import { findItem } from '@/utils/utils'
import useLifecycleAPI, { DATA_SROUCE_TYPE_MAP } from '@/hooks/controlComp/useLifecycleAPI'

import SettingDialog from './settingDialog/index'
import { Container } from './style'
import Render from './render/index'
import ContextMenuRightClick from './contextMenu/index'

const DoubleArrayTable = ({
    item, id, layoutConfig, isPdf = false
}) => {
    const widgetData = useSelector(state => state.template.widgetData)
    const { editWidget } = useWidget()
    const optSample = useSelector(state => state.project.optSample)

    const [open, setOpen] = useState(false)

    const widget = useMemo(() => {
        const findWidget = findItem(widgetData, 'widget_id', item?.widget_id)
        return findWidget
    }, [item, widgetData])

    const cacheConfig = useRef()

    const config = useMemo(() => {
        if (isEqual(cacheConfig.current, widget?.data_source)) {
            return cacheConfig.current
        }

        cacheConfig.current = widget?.data_source
        return widget?.data_source ?? {}
    }, [widget?.data_source])

    const isEdit = useMemo(() => config?.colsConfig?.some(i => i.isEdit), [config])

    // 更新持久化配置
    const updateConfig = (newConfig) => {
        editWidget({
            ...widget,
            data_source: newConfig
        })
    }

    const dataCodes = useMemo(() => config?.colsConfig?.map(c => c?.code), [config?.colsConfig])

    // daq指定的试样code
    const daqCurveSelectedSampleCodes = useMemo(() => ([optSample.code]), [optSample.code])

    const { targetRef } = useLifecycleAPI({
        controlCompId: id,
        dataSourceType: DATA_SROUCE_TYPE_MAP.二维数组,
        dataSourceCode: config?.dataSourceCode,
        dataCodes,
        timer: isEdit ? -1 : (config?.updateFreq ?? 200), // 可编辑的表格 不指定更新频率 由脚本控件更新
        number: isEdit ? -1 : (config?.showRowNumber ?? 50), // 可编辑的表格 不指定订阅条数 全部推送
        testStatus: 1, // 试验状态 二维数组表格默认试验中
        daqCurveSelectedSampleCodes
    })

    return (
        <Container
            ref={targetRef}
        >
            <Render
                id={id}
                config={config}
                isEdit={isEdit}
                isPdf={isPdf}
            />

            {
                open
                    && (
                        <SettingDialog
                            open={open}
                            setOpen={setOpen}
                            config={config}
                            updateConfig={updateConfig}
                        />
                    )
            }

            <ContextMenuRightClick
                id={id}
                layoutConfig={layoutConfig}
                setOpen={setOpen}
                config={config}
                dataCodes={dataCodes}
            />
        </Container>
    )
}

export default DoubleArrayTable
