25-08-26 09:05:53:127 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-08-26 09:05:53:131 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"b5375b43-5d5b-4669-8702-c90d003b0d9e","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-08-26 09:05:54:501 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-78639282-fa6a-4822-b502-d2144342e71a 初始化
25-08-26 09:05:54:798 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:54:800 DESKTOP-3<PERSON>REDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 09:05:55:290 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:05:55:292 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_delCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:05:55:296 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:05:55:297 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_addCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:05:55:301 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:55:303 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-55265d12-76be-4e37-9044-e0240568fa69","MsgBody":{}}
25-08-26 09:05:55:308 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:Console.WriteLine("执行判断");
int? ret = Model.station.Ccss_Connected(0);
Console.WriteLine("联机返回值"+ret);
if(ret==0){
  Model.GetVarByName<BooleanInputVar>("input_close_status").Value =true;
  return true;
}else{
    Model.GetVarByName<BooleanInputVar>("input_close_status").Value =false;
    return false;
}
25-08-26 09:05:55:580 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:05:55:583 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_close_status\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:05:55:609 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-08-26 09:05:55:611 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_2","ScriptId":"b284fe96-b2fe-4ab5-a15c-6723e4821c10","Result":false}
25-08-26 09:05:55:621 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:Console.WriteLine("执行判断");
int? ret = Model.station.Ccss_Connected(0);
Console.WriteLine("联机返回值"+ret);
if(ret==0){
  Model.GetVarByName<BooleanInputVar>("input_close_status").Value =true;
  return true;
}else{
    Model.GetVarByName<BooleanInputVar>("input_close_status").Value =false;
    return false;
}
25-08-26 09:05:55:644 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:05:55:645 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_close_status\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:05:55:648 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-08-26 09:05:55:649 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_2","ScriptId":"d857535a-2aa4-4bd2-acb1-2716317f4681","Result":false}
25-08-26 09:05:55:700 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:05:55:702 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u8054\\u673A\u0022,\r\n  \u0022Code\u0022: \u0022input_open_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:05:55:705 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:05:55:706 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u9650\\u4F4D\u0022,\r\n  \u0022Code\u0022: \u0022input_limit_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:05:55:709 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:05:55:710 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BD5\\u9A8C\\u505C\\u6B62\u0022,\r\n  \u0022Code\u0022: \u0022input_test_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:05:55:714 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:55:715 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-4efa23c4-de22-4f6a-b7e9-f6e92d563d76","MsgBody":{}}
25-08-26 09:05:55:727 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:55:728 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 09:05:55:732 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84 初始化
25-08-26 09:05:55:777 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:55:778 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 09:05:55:838 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:55:839 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"SubTaskEvalScript-f3f0dc42-cd28-4c97-b9c9-7c95fbcece0d","MsgBody":{}}
25-08-26 09:05:55:847 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:55:848 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 09:05:55:849 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 09:05:55:892 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 09:05:55:905 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:55:906 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{}}
25-08-26 09:05:55:926 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:55:927 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-08-26 09:05:55:932 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3 初始化
25-08-26 09:05:55:985 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:55:987 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{}}
25-08-26 09:05:55:994 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:55:995 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 09:05:56:044 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:05:56:047 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 09:05:56:051 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-08-26 09:05:56:180 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 09:07:58:132 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 09:07:58:134 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-08-26 09:07:58:138 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 09:07:58:141 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 09:07:58:143 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 09:07:58:150 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 09:07:58:152 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 09:07:58:153 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}}
25-08-26 09:07:58:265 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 09:07:58:324 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:07:58:326 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"daqRmc-485c6190-0361-42ff-81c5-6737d7119a1c","MsgBody":{}}
25-08-26 09:17:01:778 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-08-26 09:17:01:781 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"b8040209-16aa-41e7-832e-9c191d483a5a","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-08-26 09:17:02:999 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-78639282-fa6a-4822-b502-d2144342e71a 初始化
25-08-26 09:17:03:157 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:03:158 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 09:17:03:655 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:17:03:656 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_delCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:17:03:660 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:17:03:662 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_addCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:17:03:665 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:03:665 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-55265d12-76be-4e37-9044-e0240568fa69","MsgBody":{}}
25-08-26 09:17:03:668 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:Console.WriteLine("执行判断");
int? ret = Model.station.Ccss_Connected(0);
Console.WriteLine("联机返回值"+ret);
if(ret==0){
  Model.GetVarByName<BooleanInputVar>("input_close_status").Value =true;
  return true;
}else{
    Model.GetVarByName<BooleanInputVar>("input_close_status").Value =false;
    return false;
}
25-08-26 09:17:03:957 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:17:03:959 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_close_status\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:17:03:962 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-08-26 09:17:03:963 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_2","ScriptId":"002cba95-8844-4abc-a52d-dd6bd004af70","Result":false}
25-08-26 09:17:04:021 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:17:04:022 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u8054\\u673A\u0022,\r\n  \u0022Code\u0022: \u0022input_open_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:17:04:027 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:17:04:029 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u9650\\u4F4D\u0022,\r\n  \u0022Code\u0022: \u0022input_limit_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:17:04:034 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 09:17:04:035 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BD5\\u9A8C\\u505C\\u6B62\u0022,\r\n  \u0022Code\u0022: \u0022input_test_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 09:17:04:041 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:04:042 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-4efa23c4-de22-4f6a-b7e9-f6e92d563d76","MsgBody":{}}
25-08-26 09:17:04:066 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:04:068 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 09:17:04:072 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84 初始化
25-08-26 09:17:04:162 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:04:164 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 09:17:04:226 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:04:227 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"SubTaskEvalScript-f3f0dc42-cd28-4c97-b9c9-7c95fbcece0d","MsgBody":{}}
25-08-26 09:17:04:240 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:04:242 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 09:17:04:244 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 09:17:04:297 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 09:17:04:316 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:04:317 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{}}
25-08-26 09:17:04:343 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:04:344 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-08-26 09:17:04:349 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3 初始化
25-08-26 09:17:04:400 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:04:401 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{}}
25-08-26 09:17:04:404 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:04:406 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 09:17:04:456 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:17:04:457 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 09:17:04:459 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-08-26 09:17:04:541 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 09:51:41:110 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 09:51:41:111 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-08-26 09:51:41:114 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 09:51:41:115 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 09:51:41:116 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 09:51:41:120 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 09:51:41:121 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 09:51:41:122 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}}
25-08-26 09:51:41:198 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 09:51:41:334 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 09:51:41:335 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"daqRmc-485c6190-0361-42ff-81c5-6737d7119a1c","MsgBody":{}}
25-08-26 10:07:11:742 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-08-26 10:07:11:744 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"2a1c7def-2959-4847-869e-b1649a05e41e","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-08-26 10:07:12:572 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-78639282-fa6a-4822-b502-d2144342e71a 初始化
25-08-26 10:07:12:689 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:12:691 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:07:12:722 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:07:12:723 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_delCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:07:12:726 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:07:12:727 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_addCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:07:12:728 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:12:730 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-55265d12-76be-4e37-9044-e0240568fa69","MsgBody":{}}
25-08-26 10:07:12:731 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:Console.WriteLine("执行判断");
int? ret = Model.station.Ccss_Connected(0);
Console.WriteLine("联机返回值"+ret);
if(ret==0){
  Model.GetVarByName<BooleanInputVar>("input_close_status").Value =true;
  return true;
}else{
    Model.GetVarByName<BooleanInputVar>("input_close_status").Value =false;
    return false;
}
25-08-26 10:07:12:762 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:07:12:763 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_close_status\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:07:12:766 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-08-26 10:07:12:767 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_2","ScriptId":"c360cf1e-1088-4667-b5aa-73a5fe0e78a0","Result":false}
25-08-26 10:07:12:791 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:07:12:792 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u8054\\u673A\u0022,\r\n  \u0022Code\u0022: \u0022input_open_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:07:12:796 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:07:12:797 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u9650\\u4F4D\u0022,\r\n  \u0022Code\u0022: \u0022input_limit_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:07:12:802 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:07:12:803 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BD5\\u9A8C\\u505C\\u6B62\u0022,\r\n  \u0022Code\u0022: \u0022input_test_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:07:12:808 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:12:810 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-4efa23c4-de22-4f6a-b7e9-f6e92d563d76","MsgBody":{}}
25-08-26 10:07:12:818 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:12:819 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:07:12:824 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84 初始化
25-08-26 10:07:12:897 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:12:898 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:07:12:930 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:12:931 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"SubTaskEvalScript-f3f0dc42-cd28-4c97-b9c9-7c95fbcece0d","MsgBody":{}}
25-08-26 10:07:12:966 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:12:968 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:07:12:969 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 10:07:13:035 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:07:13:070 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:13:072 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{}}
25-08-26 10:07:13:080 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:13:083 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-08-26 10:07:13:147 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3 初始化
25-08-26 10:07:13:195 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:13:196 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{}}
25-08-26 10:07:13:205 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:13:206 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:07:13:270 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:13:273 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:07:13:275 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-08-26 10:07:13:437 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:07:25:331 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-895b0671-8511-4bc0-acb8-d9db97dc2658 初始化
25-08-26 10:07:25:387 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:25:389 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:07:25:466 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:07:25:468 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_delCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_3954c162\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:07:25:472 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:07:25:473 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_addCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_3954c162\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:07:25:477 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:25:479 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"SubTaskEvalScript-2c6ac283-a4dc-4bed-ad52-937affd00202","MsgBody":{}}
25-08-26 10:07:25:489 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:25:490 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2","MsgBody":{"Cmd":"start","InstCode":"sample_3954c162","ActionID":"0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:07:25:502 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84 初始化
25-08-26 10:07:25:564 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:25:566 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2","MsgBody":{}}
25-08-26 10:07:25:570 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:25:571 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:07:25:573 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:25:574 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:07:25:575 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-895b0671-8511-4bc0-acb8-d9db97dc2658  Stop: {:parent nil}
25-08-26 10:07:25:596 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:25:597 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"SubTaskEvalScript-f3f0dc42-cd28-4c97-b9c9-7c95fbcece0d","MsgBody":{}}
25-08-26 10:07:25:621 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:25:623 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:07:25:623 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:07:25:625 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :SubTaskID "onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2", :MsgBody {:Cmd "start", :InstCode "sample_3954c162", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 10:07:25:668 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:07:33:580 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:07:33:581 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 7,\r\n  \u0022Dimension\u0022: \u0022dimension_Force\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw13c2640\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022kN\u0022,\r\n  \u0022DisplayValue\u0022: 7,\r\n  \u0022Code\u0022: \u0022input_avgload\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_3954c162\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":false,"SendToUI":true,"Type":"input"}}
25-08-26 10:07:33:709 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-08-26 10:07:33:710 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_2","ScriptId":"e4ca82c0-e3f1-47c6-802b-83ebbb690c9d","Result":false}
25-08-26 10:07:40:219 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:07:40:220 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 7,\r\n  \u0022Dimension\u0022: \u0022dimension_Force\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw13c2640\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022kN\u0022,\r\n  \u0022DisplayValue\u0022: 7,\r\n  \u0022Code\u0022: \u0022input_avgload\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_3954c162\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":false,"SendToUI":true,"Type":"input"}}
25-08-26 10:07:40:266 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-08-26 10:07:40:267 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_2","ScriptId":"050cb09f-07de-42cc-a85a-0ef8a6c42972","Result":false}
25-08-26 10:07:42:222 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 10:07:42:223 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-08-26 10:07:42:226 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:07:42:227 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 10:07:42:228 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :SubTaskID "onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2", :MsgBody {:Cmd "start", :InstCode "sample_3954c162", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 10:07:42:230 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:07:42:232 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 10:07:42:233 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}}
25-08-26 10:07:42:240 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:07:42:241 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"daqRmc-485c6190-0361-42ff-81c5-6737d7119a1c","MsgBody":{}}
25-08-26 10:07:42:244 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daqRmc-485c6190-0361-42ff-81c5-6737d7119a1c不在执行状态, 而是 :aborted
25-08-26 10:07:42:282 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:07:42:283 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 10:07:42:284 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-895b0671-8511-4bc0-acb8-d9db97dc2658  Stop: {:parent nil}
25-08-26 10:07:42:286 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:08:32:604 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-08-26 10:08:32:605 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"4e77bbaa-c8b3-4238-bd53-43753f9c34e4","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-08-26 10:08:33:750 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-78639282-fa6a-4822-b502-d2144342e71a 初始化
25-08-26 10:08:33:980 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:33:981 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:08:34:499 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:08:34:500 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_delCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:08:34:504 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:08:34:506 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_addCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:08:34:510 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:34:512 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-55265d12-76be-4e37-9044-e0240568fa69","MsgBody":{}}
25-08-26 10:08:34:514 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:Console.WriteLine("执行判断");
int? ret = Model.station.Ccss_Connected(0);
Console.WriteLine("联机返回值"+ret);
if(ret==0){
  Model.GetVarByName<BooleanInputVar>("input_close_status").Value =true;
  return true;
}else{
    Model.GetVarByName<BooleanInputVar>("input_close_status").Value =false;
    return false;
}
25-08-26 10:08:34:740 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:08:34:741 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_close_status\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:08:34:745 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-08-26 10:08:34:745 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_2","ScriptId":"b97b03ff-b5c3-4422-ac1f-6540f0f85465","Result":false}
25-08-26 10:08:34:806 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:08:34:807 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u8054\\u673A\u0022,\r\n  \u0022Code\u0022: \u0022input_open_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:08:34:811 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:08:34:811 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u9650\\u4F4D\u0022,\r\n  \u0022Code\u0022: \u0022input_limit_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:08:34:817 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:08:34:818 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BD5\\u9A8C\\u505C\\u6B62\u0022,\r\n  \u0022Code\u0022: \u0022input_test_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:08:34:824 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:34:826 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-4efa23c4-de22-4f6a-b7e9-f6e92d563d76","MsgBody":{}}
25-08-26 10:08:34:840 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:34:842 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:08:34:845 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84 初始化
25-08-26 10:08:34:897 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:34:898 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:08:35:023 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:35:024 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"SubTaskEvalScript-f3f0dc42-cd28-4c97-b9c9-7c95fbcece0d","MsgBody":{}}
25-08-26 10:08:35:033 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:35:034 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:08:35:036 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 10:08:35:079 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:08:35:086 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:35:088 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{}}
25-08-26 10:08:35:094 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:35:095 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-08-26 10:08:35:099 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3 初始化
25-08-26 10:08:35:140 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:35:141 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{}}
25-08-26 10:08:35:144 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:35:146 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:08:35:202 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:35:203 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:08:35:204 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-08-26 10:08:35:311 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:08:41:723 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:08:41:725 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1111,\r\n  \u0022Dimension\u0022: \u0022dimension_Force\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw13c2640\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022kN\u0022,\r\n  \u0022DisplayValue\u0022: 1111,\r\n  \u0022Code\u0022: \u0022input_avgload\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":false,"SendToUI":true,"Type":"input"}}
25-08-26 10:08:41:835 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-08-26 10:08:41:837 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_2","ScriptId":"476bd163-0663-4a3a-9207-6459e76a635b","Result":false}
25-08-26 10:08:45:870 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-895b0671-8511-4bc0-acb8-d9db97dc2658 初始化
25-08-26 10:08:45:938 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:45:939 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:08:46:013 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:08:46:014 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_delCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_3954c162\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:08:46:018 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:08:46:019 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_addCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_3954c162\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:08:46:022 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:46:023 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"SubTaskEvalScript-2c6ac283-a4dc-4bed-ad52-937affd00202","MsgBody":{}}
25-08-26 10:08:46:028 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:46:029 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2","MsgBody":{"Cmd":"start","InstCode":"sample_3954c162","ActionID":"0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:08:46:033 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84 初始化
25-08-26 10:08:46:082 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:46:083 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2","MsgBody":{}}
25-08-26 10:08:46:087 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:46:089 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:08:46:090 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:46:091 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:08:46:092 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-895b0671-8511-4bc0-acb8-d9db97dc2658  Stop: {:parent nil}
25-08-26 10:08:46:118 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:46:120 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"SubTaskEvalScript-f3f0dc42-cd28-4c97-b9c9-7c95fbcece0d","MsgBody":{}}
25-08-26 10:08:46:133 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:46:134 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:08:46:135 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :SubTaskID "onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2", :MsgBody {:Cmd "start", :InstCode "sample_3954c162", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 10:08:46:145 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:08:46:184 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:08:46:188 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:08:46:190 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2","MsgBody":{}}
25-08-26 10:09:43:774 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-895b0671-8511-4bc0-acb8-d9db97dc2658 初始化
25-08-26 10:09:43:822 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:09:43:823 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:09:43:873 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:09:43:875 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_delCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:09:43:877 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:09:43:878 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_addCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:09:43:879 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:09:43:881 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"SubTaskEvalScript-2c6ac283-a4dc-4bed-ad52-937affd00202","MsgBody":{}}
25-08-26 10:09:43:921 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:09:43:923 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:09:43:926 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84 初始化
25-08-26 10:09:44:001 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:09:44:002 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2","MsgBody":{}}
25-08-26 10:09:44:005 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:09:44:006 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:09:44:030 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:09:44:032 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:09:44:034 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-895b0671-8511-4bc0-acb8-d9db97dc2658  Stop: {:parent nil}
25-08-26 10:09:44:146 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:09:44:180 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:09:44:182 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"SubTaskEvalScript-f3f0dc42-cd28-4c97-b9c9-7c95fbcece0d","MsgBody":{}}
25-08-26 10:09:44:191 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:09:44:193 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:09:44:195 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :SubTaskID "onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 10:09:44:254 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:09:44:257 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:09:44:258 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-895b0671-8511-4bc0-acb8-d9db97dc2658","SubTaskID":"onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2","MsgBody":{}}
25-08-26 10:10:03:030 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:10:03:031 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 546,\r\n  \u0022Dimension\u0022: \u0022dimension_Force\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw13c2640\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022kN\u0022,\r\n  \u0022DisplayValue\u0022: 546,\r\n  \u0022Code\u0022: \u0022input_avgload\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":false,"SendToUI":true,"Type":"input"}}
25-08-26 10:10:03:165 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-08-26 10:10:03:166 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_2","ScriptId":"f1520928-586f-4611-a49b-7b6798124efb","Result":false}
25-08-26 10:10:16:321 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 10:10:16:322 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-08-26 10:10:16:324 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:10:16:326 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 10:10:16:326 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-895b0671-8511-4bc0-acb8-d9db97dc2658", :SubTaskID "onlyAction-1c8bb276-50b5-44f8-b7fa-cfa057d2d6b2", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 10:10:16:329 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:10:16:330 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 10:10:16:332 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}}
25-08-26 10:10:16:457 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:10:16:458 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-08-26 10:10:16:459 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-895b0671-8511-4bc0-acb8-d9db97dc2658  Stop: {:parent nil}
25-08-26 10:10:16:462 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:10:16:498 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:16:499 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"daqRmc-485c6190-0361-42ff-81c5-6737d7119a1c","MsgBody":{}}
25-08-26 10:10:42:559 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-08-26 10:10:42:560 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"38c0f334-dc03-438d-928a-3c074fd539b1","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-08-26 10:10:43:196 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-78639282-fa6a-4822-b502-d2144342e71a 初始化
25-08-26 10:10:43:258 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:260 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:10:43:290 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:10:43:292 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_delCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:10:43:296 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:10:43:297 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_addCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_408c390a\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:10:43:300 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:303 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-55265d12-76be-4e37-9044-e0240568fa69","MsgBody":{}}
25-08-26 10:10:43:305 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:Console.WriteLine("执行判断");
int? ret = Model.station.Ccss_Connected(0);
Console.WriteLine("联机返回值"+ret);
if(ret==0){
  Model.GetVarByName<BooleanInputVar>("input_close_status").Value =true;
  return true;
}else{
    Model.GetVarByName<BooleanInputVar>("input_close_status").Value =false;
    return false;
}
25-08-26 10:10:43:329 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:10:43:331 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_close_status\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:10:43:336 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-08-26 10:10:43:338 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_2","ScriptId":"8af04d38-9aa7-48a1-b168-48b1f12ac467","Result":false}
25-08-26 10:10:43:377 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:10:43:378 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u8054\\u673A\u0022,\r\n  \u0022Code\u0022: \u0022input_open_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:10:43:384 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:10:43:385 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u9650\\u4F4D\u0022,\r\n  \u0022Code\u0022: \u0022input_limit_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:10:43:389 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-08-26 10:10:43:390 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BD5\\u9A8C\\u505C\\u6B62\u0022,\r\n  \u0022Code\u0022: \u0022input_test_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_2\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-08-26 10:10:43:392 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:393 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-4efa23c4-de22-4f6a-b7e9-f6e92d563d76","MsgBody":{}}
25-08-26 10:10:43:525 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:526 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-08-26 10:10:43:641 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84 初始化
25-08-26 10:10:43:699 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:700 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:10:43:736 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:738 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"SubTaskEvalScript-f3f0dc42-cd28-4c97-b9c9-7c95fbcece0d","MsgBody":{}}
25-08-26 10:10:43:765 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:766 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:10:43:767 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_2", :ProcessID "project_2-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_408c390a", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-08-26 10:10:43:818 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-08-26 10:10:43:821 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:822 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{}}
25-08-26 10:10:43:837 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:839 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{"Cmd":"start","InstCode":"sample_408c390a","ActionID":"cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-08-26 10:10:43:842 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3 初始化
25-08-26 10:10:43:895 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:896 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{}}
25-08-26 10:10:43:898 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:899 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"start-node-action","MsgBody":{}}
25-08-26 10:10:43:972 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-08-26 10:10:43:973 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_2","ProcessID":"project_2-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"end-node-action","MsgBody":{}}
25-08-26 10:10:43:975 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_2-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-08-26 10:10:44:104 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
