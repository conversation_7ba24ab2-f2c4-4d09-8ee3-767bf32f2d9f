import React, {
    useState, useEffect, useCallback, useMemo, useRef
} from 'react'
import { useDispatch, useSelector } from 'react-redux'
import isEqual from 'lodash/isEqual'

import ChartXY from '@/components/charts/ChartXY/index'
import { findItem } from '@/utils/utils'
import useWidget from '@/hooks/useWidget'

import { convertOldConfigToNew } from './utils/convertOldConfigToNew'
import CompRender from '../CurveDoubleArray/CompRender'

const CurveDaqBuffer = ({
    id, item, layoutConfig,
    isRightClick = true, isFission
}) => {
    // 老配置
    const currentSettingIdFromWidget = item?.widget_data_source ? JSON.parse(item?.widget_data_source) : null
    const oldSetting = useSelector(state => state.staticCurve?.settingResList.find(i => i.id === currentSettingIdFromWidget))

    // 新配置
    const widgetData = useSelector(state => state.template.widgetData)
    const widget = useMemo(() => findItem(widgetData, 'widget_id', item?.widget_id), [item, widgetData])
    const { editWidget } = useWidget()

    const [config, setConfig] = useState()

    useEffect(() => {
        // 判断有没有新配置
        if (widget?.data_source && widget?.data_source?.curveGroup) {
            // 同步配置
            if (!isEqual(config, widget?.data_source)) {
                setConfig(widget?.data_source)
            }

            return
        }

        // 没有新配置则转换老配置
        if (oldSetting) {
            const newConfig = convertOldConfigToNew(oldSetting)
            console.log('配置', oldSetting, newConfig)
            // 更新
            updateConfig(newConfig)
        }

        // 啥都没有则应用默认配置
        // setConfig(initialOption)
    }, [widget, oldSetting])

    // 新配置 更新持久化配置
    const updateConfig = (newConfig) => {
        setConfig(newConfig)

        editWidget({
            ...widget,
            data_source: newConfig
        })
    }

    // 区分配置和状态
    const compConfigCacheRef = useRef()
    const compConfig = useMemo(() => {
        if (!config) {
            return null
        }
        const { compStatus, ...c } = config

        if (isEqual(compConfigCacheRef.current, c)) {
            return compConfigCacheRef.current
        }

        compConfigCacheRef.current = c

        return c
    }, [config])

    const compStatusCacheRef = useRef()
    const compStatus = useMemo(() => {
        if (!config) {
            return null
        }
        const { compStatus: s } = config

        if (isEqual(compStatusCacheRef.current, s)) {
            return compStatusCacheRef.current
        }

        compStatusCacheRef.current = s

        return s
    }, [config])

    return (
        // <></>
        <CompRender
            id={id}
            layoutConfig={layoutConfig}
            config={compConfig}
            compStatus={compStatus}
            updateConfig={updateConfig}
            isBufferCurve
        />
    )
}

export default CurveDaqBuffer
