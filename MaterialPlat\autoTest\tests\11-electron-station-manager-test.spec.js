// @ts-check
const { test, expect } = require('@playwright/test');
const { _electron: electron } = require('playwright');
const path = require('path');
const { execSync } = require('child_process');
const LogCollector = require('../scripts/collect-logs');

/**
 * 测试配置开关
 * 
 * 使用说明：
 * 1. enableAdvancedTests: 主开关，控制步骤11之后的所有高级测试功能
 *    - true: 启用所有高级测试（默认）
 *    - false: 跳过步骤11之后的所有操作，只执行基础测试
 * 
 * 2. enableCloseFlowTest: 控制应用关闭流程测试
 *    - true: 测试关闭确认对话框和正常退出流程（默认）
 *    - false: 跳过关闭流程测试，直接关闭应用
 * 
 * 3. enableLogCollection: 控制日志收集功能
 *    - true: 收集测试期间的后台服务日志（默认）
 *    - false: 跳过日志收集，提高测试执行速度
 * 
 * 调试建议：
 * - 开发阶段：可设置 enableAdvancedTests = false 快速验证基础功能
 * - 性能测试：可设置 enableLogCollection = false 减少I/O开销
 * - 问题排查：保持所有开关为 true 获取完整的测试信息
 */
const TEST_CONFIG = {
  enableAdvancedTests: true,   // 主开关：控制步骤11之后的所有高级测试功能
  enableCloseFlowTest: true,   // 控制应用关闭流程测试
  enableLogCollection: true    // 控制日志收集功能
};

test.describe('Electron应用站管理器测试', () => {
  let electronApp;
  let window;
  const electronDir = path.resolve(__dirname, '../../electron');

  test.afterEach(async () => {
    // 清理：确保Electron应用已关闭
    if (electronApp) {
      try {
        console.log('🧹 开始全局测试环境清理...');
        
        // 检查应用是否已经关闭
        try {
          const windows = electronApp.windows();
          if (windows.length > 0) {
            console.log('⚠️ 应用仍在运行，执行强制关闭...');
            // 设置超时，避免长时间等待
            await Promise.race([
              electronApp.close(),
              new Promise((_, reject) => setTimeout(() => reject(new Error('关闭超时')), 5000))
            ]);
          } else {
            console.log('✅ 应用已正常关闭');
          }
        } catch (closeCheckError) {
          console.log('ℹ️ 应用已关闭或无法检查状态:', closeCheckError.message);
        }
        
        console.log('  清理临时测试数据...');
        console.log('✅ 全局测试环境清理完成');
        
      } catch (error) {
        console.warn('⚠️ 清理过程中出现问题:', error.message);
      }
      
      electronApp = null;
      window = null;
    }
  });

  test('启动Electron应用，登录并测试站管理器功能', async ({ }, testInfo) => {
    console.log('🚀 开始Electron应用站管理器测试');
    
    // 记录测试开始时间
    const testStartTime = new Date();
    console.log(`⏰ 测试开始时间: ${testStartTime.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })} (本地时间)`);
    console.log(`⏰ 测试开始时间(UTC): ${testStartTime.toISOString()}`);
    
    try {
      // 步骤1: 启动Electron应用
      console.log('📂 Electron应用目录:', electronDir);
      
      electronApp = await electron.launch({
        args: [electronDir],
        timeout: 60000,
        env: {
          ...process.env,
          NODE_ENV: 'test',
          ELECTRON_DISABLE_SECURITY_WARNINGS: 'true'
        }
      });
      
      console.log('✅ Electron应用启动成功');
      
      // 步骤2: 等待窗口出现
      console.log('⏳ 等待窗口出现...');
      await electronApp.waitForEvent('window', { timeout: 30000 });
      
      // 步骤3: 获取主窗口
      window = await electronApp.firstWindow();
      console.log('✅ 获取到主窗口');
      
      // 明确禁用自动对话框处理，让测试能够手动处理对话框
      window.on('dialog', async dialog => {
        // 不自动处理对话框，让测试代码手动处理
        console.log('ℹ️ 检测到对话框，但不自动处理:', dialog.message());
      });
      
      // 步骤4: 等待页面加载
      console.log('⏳ 等待页面加载...');
      await window.waitForLoadState('load', { timeout: 30000 });
      await window.waitForTimeout(3000); // 额外等待确保页面完全加载
      
      // 步骤5: 验证基本功能
      const title = await window.title();
      expect(title).toBeTruthy();
      console.log(`✅ 应用标题: ${title}`);
      
      const url = window.url();
      expect(url).toBeTruthy();
      console.log(`✅ 应用URL: ${url}`);
      
      // 验证窗口可见性
      const isVisible = await window.isVisible('body');
      expect(isVisible).toBeTruthy();
      console.log('✅ 应用界面可见');
      
      // 获取窗口大小
      const viewportSize = window.viewportSize();
      console.log(`📏 窗口大小: ${viewportSize?.width}x${viewportSize?.height}`);
      
      // 截图1: 应用启动后的主界面
      const testCaseName = '11-electron-station-manager-test';
      const screenshotPath = testInfo.outputPath(`${testCaseName}-01-app-main-interface.png`);
      await window.screenshot({ path: screenshotPath, fullPage: true });
      console.log(`📸 已保存主界面截图: ${testCaseName}-01-app-main-interface.png`);
      await testInfo.attach(`${testCaseName}-01-app-main-interface.png`, { path: screenshotPath });
      
      // 步骤6: 查找并点击登录按钮
      console.log('🔍 查找登录按钮...');
      
      // 尝试多种可能的登录按钮选择器
      const loginSelectors = [
        'button:has-text("登录")',
        'button:has-text("登陆")',
        'button:has-text("Login")',
        'input[type="submit"][value*="登录"]',
        'input[type="submit"][value*="登陆"]',
        'input[type="button"][value*="登录"]',
        'input[type="button"][value*="登陆"]',
        '.login-btn',
        '#login-btn',
        '[data-testid="login-button"]'
      ];
      
      let loginButton = null;
      let usedSelector = '';
      
      for (const selector of loginSelectors) {
        try {
          const element = window.locator(selector).first();
          if (await element.isVisible({ timeout: 2000 })) {
            loginButton = element;
            usedSelector = selector;
            console.log(`✅ 找到登录按钮，使用选择器: ${selector}`);
            break;
          }
        } catch (error) {
          // 继续尝试下一个选择器
        }
      }
      
      if (!loginButton) {
        // 如果没有找到登录按钮，尝试查找页面上的所有按钮
        console.log('⚠️ 未找到标准登录按钮，尝试查找所有按钮...');
        const allButtons = await window.locator('button, input[type="submit"], input[type="button"]').all();
        
        for (let i = 0; i < allButtons.length; i++) {
          const button = allButtons[i];
          try {
            const text = await button.textContent() || await button.getAttribute('value') || '';
            console.log(`按钮 ${i + 1}: "${text}"`);
            if (text.includes('登录') || text.includes('登陆') || text.toLowerCase().includes('login')) {
              loginButton = button;
              usedSelector = `button:nth-child(${i + 1})`;
              console.log(`✅ 找到可能的登录按钮: "${text}"`);
              break;
            }
          } catch (error) {
            // 忽略错误，继续查找
          }
        }
      }
      
      if (!loginButton) {
        throw new Error('未找到登录按钮，请检查页面结构');
      }
      
      // 截图2: 登录前的界面
      const beforeLoginPath = testInfo.outputPath(`${testCaseName}-02-before-login.png`);
      await window.screenshot({ path: beforeLoginPath, fullPage: true });
      console.log(`📸 已保存登录前界面截图: ${testCaseName}-02-before-login.png`);
      await testInfo.attach(`${testCaseName}-02-before-login.png`, { path: beforeLoginPath });
      
      // 步骤7: 点击登录按钮
      console.log('🖱️ 点击登录按钮...');
      await loginButton.click({ force: true });
      console.log('✅ 已点击登录按钮');
      
      // 等待登录处理
      await window.waitForTimeout(3000);
      
      // 截图3: 登录后的界面
      const afterLoginPath = testInfo.outputPath(`${testCaseName}-03-after-login.png`);
      await window.screenshot({ path: afterLoginPath, fullPage: true });
      console.log(`📸 已保存登录后界面截图: ${testCaseName}-03-after-login.png`);
      await testInfo.attach(`${testCaseName}-03-after-login.png`, { path: afterLoginPath });
      
      // 步骤8: 查找并点击站管理器
      console.log('🔍 查找站管理器入口...');
      
      // 使用用户提供的精确XPath路径
      const stationManagerXPath = '/html/body/div/div[2]/div/div[3]/div[3]/div/div[1]/div/div/div[2]/div/div[2]/div/div';
      
      // 尝试多种可能的站管理器选择器，优先使用XPath
      const stationManagerSelectors = [
        `xpath=${stationManagerXPath}`,
        'button:has-text("站管理器")',
        'a:has-text("站管理器")',
        'li:has-text("站管理器")',
        '.menu-item:has-text("站管理器")',
        '[data-testid="station-manager"]',
        'button:has-text("Station Manager")',
        'a:has-text("Station Manager")',
        '.station-manager-btn',
        '#station-manager',
        'button:has-text("站点管理")',
        'a:has-text("站点管理")',
        'button:has-text("管理器")',
        'a:has-text("管理器")'
      ];
      
      let stationManagerButton = null;
      let usedStationSelector = '';
      
      for (const selector of stationManagerSelectors) {
        try {
          const element = window.locator(selector).first();
          if (await element.isVisible({ timeout: 2000 })) {
            stationManagerButton = element;
            usedStationSelector = selector;
            console.log(`✅ 找到站管理器入口，使用选择器: ${selector}`);
            
            // 验证元素内容是否包含"站管理器"
            try {
              const elementText = await element.textContent();
              console.log(`📋 站管理器元素内容: "${elementText}"`);
            } catch (textError) {
              console.log('ℹ️ 无法获取元素文本，但元素已找到');
            }
            break;
          }
        } catch (error) {
          // 继续尝试下一个选择器
        }
      }
      
      if (!stationManagerButton) {
        // 如果没有找到站管理器按钮，尝试查找页面上的所有可点击元素
        console.log('⚠️ 未找到标准站管理器入口，尝试查找所有可点击元素...');
        const allClickable = await window.locator('button, a, li, .menu-item, [role="button"], .nav-item, div[role="button"], div').all();
        
        for (let i = 0; i < Math.min(allClickable.length, 50); i++) {
          const element = allClickable[i];
          try {
            const text = await element.textContent() || '';
            if (text.trim()) {
              console.log(`可点击元素 ${i + 1}: "${text.trim()}"`);
              if (text.includes('站管理器') || text.includes('站点管理') || text.includes('Station Manager') || 
                  (text.includes('站') && text.includes('管理')) || text.includes('管理器')) {
                stationManagerButton = element;
                usedStationSelector = `clickable:nth-child(${i + 1})`;
                console.log(`✅ 找到可能的站管理器入口: "${text.trim()}"`);
                break;
              }
            }
          } catch (error) {
            // 忽略错误，继续查找
          }
        }
      }
      
      if (!stationManagerButton) {
        throw new Error('未找到站管理器入口，请检查页面结构或确认功能是否可用');
      }
      
      // 步骤9: 点击进入站管理器
      console.log('🖱️ 点击进入站管理器...');
      await stationManagerButton.click({ force: true });
      console.log('✅ 已点击站管理器入口');
      
      // 等待站管理器页面加载
      await window.waitForTimeout(3000);
      
      // 截图4: 站管理器界面
      const stationManagerInterfacePath = testInfo.outputPath(`${testCaseName}-04-station-manager-interface.png`);
      await window.screenshot({ path: stationManagerInterfacePath, fullPage: true });
      console.log(`📸 已保存站管理器界面截图: ${testCaseName}-04-station-manager-interface.png`);
      await testInfo.attach(`${testCaseName}-04-station-manager-interface.png`, { path: stationManagerInterfacePath });
      
      // 验证是否成功进入站管理器页面
      const currentUrl = window.url();
      console.log(`📍 当前页面URL: ${currentUrl}`);
      
      // 尝试验证页面内容是否包含站管理器相关元素
      try {
        const pageContent = await window.textContent('body');
        if (pageContent?.includes('站管理器') || pageContent?.includes('Station Manager') ||
            (pageContent?.includes('站点管理') || pageContent?.includes('管理器'))) {
          console.log('✅ 确认已成功进入站管理器页面');
        } else {
          console.log('⚠️ 页面内容未明确显示站管理器相关信息，但继续测试流程');
        }
      } catch (contentError) {
        console.log('ℹ️ 无法验证页面内容，但继续测试流程');
      }
      
      // 步骤10: 在站管理器页面停留5秒钟
      console.log('⏳ 在站管理器页面停留5秒钟...');
      await window.waitForTimeout(5000);
      console.log('✅ 停留完成');
      
      // 截图5: 停留期间的站管理器界面状态
      const stationManagerStatePath = testInfo.outputPath(`${testCaseName}-05-station-manager-state.png`);
      await window.screenshot({ path: stationManagerStatePath, fullPage: true });
      console.log(`📸 已保存停留期间站管理器界面截图: ${testCaseName}-05-station-manager-state.png`);
      await testInfo.attach(`${testCaseName}-05-station-manager-state.png`, { path: stationManagerStatePath });
      
      // 步骤11: 测试站的新建功能
      console.log('🏗️ 开始测试站的新建功能...');
      
      // 查找并点击新建站按钮（可能的选择器）
       const createStationSelectors = [
         '/html/body/div[2]/div/div/div/div[1]/div/div/div[2]/div/div[1]/div/div/div[1]/div[2]/div/div/div[2]/button', // 用户提供的精确XPath
         'button:has-text("新建站")',
         'button:has-text("添加站")',
         'button:has-text("创建站")',
         '[data-testid="create-station"]',
         '.create-station-btn',
         '#create-station',
         'button[title*="新建"]',
         'button[title*="添加"]'
       ];
      
      let createStationButton = null;
       for (const selector of createStationSelectors) {
         try {
           // 如果是XPath选择器，使用xpath方法
           if (selector.startsWith('/html') || selector.startsWith('//')) {
             createStationButton = await window.locator(`xpath=${selector}`).first();
           } else {
             createStationButton = await window.locator(selector).first();
           }
           
           if (await createStationButton.isVisible({ timeout: 2000 })) {
             console.log(`✅ 找到新建站按钮: ${selector}`);
             break;
           }
         } catch (error) {
           // 继续尝试下一个选择器
           console.log(`⚠️ 选择器 ${selector} 未找到元素`);
         }
       }
      
      if (createStationButton && await createStationButton.isVisible()) {
        // 点击新建站按钮
        await createStationButton.click();
        console.log('🖱️ 已点击新建站按钮');
        await window.waitForTimeout(2000);
        
        // 截图6: 新建站对话框或页面
        const createStationDialogPath = testInfo.outputPath(`${testCaseName}-06-create-station-dialog.png`);
        await window.screenshot({ path: createStationDialogPath, fullPage: true });
        console.log(`📸 已保存新建站对话框截图: ${testCaseName}-06-create-station-dialog.png`);
        await testInfo.attach(`${testCaseName}-06-create-station-dialog.png`, { path: createStationDialogPath });
        
        // 查找站名输入框并输入测试数据
        const stationNameSelectors = [
          '/html/body/div[3]/div/div[2]/div/div[1]/div/div[2]/form/div[1]/div/div[2]/div/div/input', // 用户提供的站名输入框XPath
          'input[placeholder*="站名"]',
          'input[placeholder*="名称"]',
          'input[name="stationName"]',
          'input[name="name"]',
          '[data-testid="station-name-input"]',
          '.station-name-input',
          '#stationName'
        ];
        
        let stationNameInput = null;
        for (const selector of stationNameSelectors) {
          try {
            // 如果是XPath选择器，使用xpath方法
            if (selector.startsWith('/html') || selector.startsWith('//')) {
              stationNameInput = await window.locator(`xpath=${selector}`).first();
            } else {
              stationNameInput = await window.locator(selector).first();
            }
            
            if (await stationNameInput.isVisible({ timeout: 2000 })) {
              console.log(`✅ 找到站名输入框: ${selector}`);
              break;
            }
          } catch (error) {
            console.log(`⚠️ 站名输入框选择器 ${selector} 未找到元素`);
          }
        }
        
        if (stationNameInput && await stationNameInput.isVisible()) {
          const testStationName = `测试站_${Date.now()}`;
          await stationNameInput.fill(testStationName);
          console.log(`📝 已输入站名: ${testStationName}`);
          await window.waitForTimeout(1000);
          
          // 查找并填写内部名称输入框
          const internalNameSelectors = [
            '/html/body/div[3]/div/div[2]/div/div[1]/div/div[2]/form/div[2]/div/div[2]/div/div/input', // 用户提供的内部名称输入框XPath
            'input[placeholder*="内部名称"]',
            'input[name="internalName"]',
            '[data-testid="internal-name-input"]',
            '.internal-name-input'
          ];
          
          let internalNameInput = null;
          for (const selector of internalNameSelectors) {
            try {
              if (selector.startsWith('/html') || selector.startsWith('//')) {
                internalNameInput = await window.locator(`xpath=${selector}`).first();
              } else {
                internalNameInput = await window.locator(selector).first();
              }
              
              if (await internalNameInput.isVisible({ timeout: 2000 })) {
                console.log(`✅ 找到内部名称输入框: ${selector}`);
                break;
              }
            } catch (error) {
              console.log(`⚠️ 内部名称输入框选择器 ${selector} 未找到元素`);
            }
          }
          
          if (internalNameInput && await internalNameInput.isVisible()) {
            const testInternalName = `internal_${Date.now()}`;
            await internalNameInput.fill(testInternalName);
            console.log(`📝 已输入内部名称: ${testInternalName}`);
            await window.waitForTimeout(1000);
          } else {
            console.log('⚠️ 未找到内部名称输入框，跳过输入操作');
          }
          
          // 查找并点击确认/保存按钮
          const confirmSelectors = [
            '/html/body/div[3]/div/div[2]/div/div[1]/div/div[3]/button[2]', // 用户提供的确定按钮XPath
            'button:has-text("确认")',
            'button:has-text("保存")',
            'button:has-text("创建")',
            'button:has-text("提交")',
            '[data-testid="confirm-btn"]',
            '.confirm-btn',
            '.save-btn',
            '#confirm',
            'button[type="submit"]'
          ];
          
          let confirmButton = null;
          for (const selector of confirmSelectors) {
            try {
              // 如果是XPath选择器，使用xpath方法
              if (selector.startsWith('/html') || selector.startsWith('//')) {
                confirmButton = await window.locator(`xpath=${selector}`).first();
              } else {
                confirmButton = await window.locator(selector).first();
              }
              
              if (await confirmButton.isVisible({ timeout: 2000 })) {
                console.log(`✅ 找到确认按钮: ${selector}`);
                break;
              }
            } catch (error) {
              console.log(`⚠️ 确认按钮选择器 ${selector} 未找到元素`);
            }
          }
          
          if (confirmButton && await confirmButton.isVisible()) {
            await confirmButton.click();
            console.log('🖱️ 已点击确认按钮');
            await window.waitForTimeout(3000); // 等待创建完成
            
            // 截图7: 创建完成后的站管理器页面
            const afterCreatePath = testInfo.outputPath(`${testCaseName}-07-after-create-station.png`);
            await window.screenshot({ path: afterCreatePath, fullPage: true });
            console.log(`📸 已保存创建完成后的截图: ${testCaseName}-07-after-create-station.png`);
            await testInfo.attach(`${testCaseName}-07-after-create-station.png`, { path: afterCreatePath });
            
            console.log('✅ 站新建功能测试完成');
            
            // 等待页面更新，确保新建的站显示在列表中
            await window.waitForTimeout(3000);
            
            // 查找并点击cfg管理按钮
            console.log('🔧 开始测试cfg管理功能...');
            const cfgManageSelectors = [
              '/html/body/div[2]/div/div/div/div[1]/div/div/div[2]/div/div[1]/div/div/div[2]/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[4]/div/div[3]/a', // 用户提供的cfg管理按钮XPath
              'a:has-text("cfg管理")',
              'a:has-text("配置管理")',
              '[data-testid="cfg-manage"]',
              '.cfg-manage-btn',
              'a[title*="cfg"]',
              'a[title*="配置"]'
            ];
            
            let cfgManageButton = null;
            for (const selector of cfgManageSelectors) {
              try {
                // 如果是XPath选择器，使用xpath方法
                if (selector.startsWith('/html') || selector.startsWith('//')) {
                  cfgManageButton = await window.locator(`xpath=${selector}`).first();
                } else {
                  cfgManageButton = await window.locator(selector).first();
                }
                
                if (await cfgManageButton.isVisible({ timeout: 3000 })) {
                  console.log(`✅ 找到cfg管理按钮: ${selector}`);
                  break;
                }
              } catch (error) {
                console.log(`⚠️ cfg管理按钮选择器 ${selector} 未找到元素`);
              }
            }
            
            if (cfgManageButton && await cfgManageButton.isVisible()) {
              await cfgManageButton.click();
              console.log('🖱️ 已点击cfg管理按钮');
              await window.waitForTimeout(3000); // 等待cfg配置页面加载
              
              // 截图8: cfg配置页面
              const cfgPagePath = testInfo.outputPath(`${testCaseName}-08-cfg-config-page.png`);
              await window.screenshot({ path: cfgPagePath, fullPage: true });
              console.log(`📸 已保存cfg配置页面截图: ${testCaseName}-08-cfg-config-page.png`);
              await testInfo.attach(`${testCaseName}-08-cfg-config-page.png`, { path: cfgPagePath });
              
              // 检查cfg配置页面是否正确加载
              const cfgPageIndicators = [
                'text=配置',
                'text=cfg',
                '[data-testid="cfg-config"]',
                '.cfg-config-page',
                '.config-panel'
              ];
              
              let cfgPageLoaded = false;
              for (const indicator of cfgPageIndicators) {
                try {
                  const element = await window.locator(indicator).first();
                  if (await element.isVisible({ timeout: 2000 })) {
                    console.log(`✅ cfg配置页面已加载，检测到: ${indicator}`);
                    cfgPageLoaded = true;
                    break;
                  }
                } catch (error) {
                  // 继续检查下一个指示器
                }
              }
              
              if (cfgPageLoaded) {
                console.log('✅ cfg配置页面加载成功');
                
                // 步骤12: 在cfg配置页面点击新建按钮
                console.log('🔧 开始测试cfg配置页面新建功能...');
                
                // 等待页面稳定
                await window.waitForTimeout(2000);
                
                // 定义cfg新建按钮选择器
                const cfgCreateSelectors = [
                  '/html/body/div[3]/div/div/div/div[1]/div/div/div[2]/div/div[1]/div/div/div[1]/div[2]/div/div/div[1]/div/div[1]/img', // 用户提供的精确XPath
                  'button:has-text("新建")',
                  'button:has-text("添加")',
                  'button:has-text("创建")',
                  '[data-testid="cfg-create"]',
                  '.cfg-create-btn',
                  'button[title*="新建"]',
                  'button[title*="添加"]'
                ];
                
                let cfgCreateButtonFound = false;
                for (const selector of cfgCreateSelectors) {
                  try {
                    let cfgCreateButton = null;
                    // 检查是否是XPath格式
                    if (selector.startsWith('/html') || selector.startsWith('//')) {
                      cfgCreateButton = await window.locator(`xpath=${selector}`).first();
                    } else {
                      cfgCreateButton = await window.locator(selector).first();
                    }
                    
                    if (await cfgCreateButton.isVisible({ timeout: 3000 })) {
                      console.log(`✅ 找到cfg新建按钮: ${selector}`);
                      
                      // 点击cfg新建按钮
                      await cfgCreateButton.click();
                      console.log('🖱️ 已点击cfg新建按钮');
                      
                      cfgCreateButtonFound = true;
                      break;
                    }
                  } catch (error) {
                    console.log(`⚠️ 选择器 ${selector} 未找到cfg新建按钮`);
                  }
                }
                
                if (!cfgCreateButtonFound) {
                  console.log('❌ 未找到cfg新建按钮');
                } else {
                  // 等待新建对话框或页面加载
                  await window.waitForTimeout(2000);
                  
                  // 截图9: cfg新建页面
                  const cfgCreateDialogPath = testInfo.outputPath(`${testCaseName}-09-cfg-create-dialog.png`);
                  await window.screenshot({ path: cfgCreateDialogPath, fullPage: true });
                  console.log(`📸 已保存cfg新建页面截图: ${testCaseName}-09-cfg-create-dialog.png`);
                  await testInfo.attach(`${testCaseName}-09-cfg-create-dialog.png`, { path: cfgCreateDialogPath });
                  
                  console.log('✅ cfg配置页面新建功能测试完成');
                   
                   // 步骤13: 物理硬件菜单操作测试
                   console.log('🔧 开始测试物理硬件菜单操作...');
                   
                   // 等待页面稳定
                   await window.waitForTimeout(2000);
                   
                   // 步骤13.1: 右键点击物理硬件菜单
                   const physicalHardwareXPath = '/html/body/div[4]/div/div/div/div[1]/div/div/div[2]/div/div[1]/div/div[2]/div[1]/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]';
                   try {
                     const physicalHardwareElement = window.locator(`xpath=${physicalHardwareXPath}`);
                     await physicalHardwareElement.waitFor({ timeout: 5000 });
                     console.log('✅ 找到物理硬件菜单元素');
                     
                     // 模拟鼠标右键点击
                     await physicalHardwareElement.click({ button: 'right' });
                     console.log('🖱️ 已右键点击物理硬件菜单');
                     
                     // 等待右键菜单出现
                     await window.waitForTimeout(1000);
                     
                     // 步骤13.2: 左键点击弹出的div
                     const contextMenuXPath = '/html/body/div[1]/div[1]/div/div/div/div/div/div';
                     try {
                       const contextMenuElement = window.locator(`xpath=${contextMenuXPath}`);
                       await contextMenuElement.waitFor({ timeout: 3000 });
                       console.log('✅ 找到右键菜单div元素');
                       
                       // 模拟鼠标左键点击
                       await contextMenuElement.click();
                       console.log('🖱️ 已左键点击右键菜单div');
                       
                       // 等待页面响应
                       await window.waitForTimeout(1000);
                       
                       // 步骤13.3: 点击展开操作按钮
                       const expandButtonXPath = '/html/body/div[4]/div/div/div/div[1]/div/div/div[2]/div/div[1]/div/div[2]/div[1]/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/button';
                       try {
                         const expandButtonElement = window.locator(`xpath=${expandButtonXPath}`);
                         await expandButtonElement.waitFor({ timeout: 3000 });
                         console.log('✅ 找到展开操作按钮');
                         
                         // 模拟鼠标左键点击
                         await expandButtonElement.click();
                         console.log('🖱️ 已左键点击展开操作按钮');
                         
                         // 等待展开完成
                         await window.waitForTimeout(1000);
                         
                         // 步骤13.4: 点击展开后的div元素
                         const expandedDivXPath = '/html/body/div[4]/div/div/div/div[1]/div/div/div[2]/div/div[1]/div/div[2]/div[1]/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[2]/div/div';
                         try {
                           const expandedDivElement = window.locator(`xpath=${expandedDivXPath}`);
                           await expandedDivElement.waitFor({ timeout: 3000 });
                           console.log('✅ 找到展开后的div元素');
                           
                           // 模拟鼠标左键点击
                           await expandedDivElement.click();
                           console.log('🖱️ 已左键点击展开后的div元素');
                           
                           // 等待操作完成
                           await window.waitForTimeout(1000);
                           
                           // 截图10: 物理硬件操作完成后的页面
                           const physicalHardwareCompletePath = testInfo.outputPath(`${testCaseName}-10-physical-hardware-operations.png`);
                           await window.screenshot({ path: physicalHardwareCompletePath, fullPage: true });
                           console.log(`📸 已保存物理硬件操作完成截图: ${testCaseName}-10-physical-hardware-operations.png`);
                           await testInfo.attach(`${testCaseName}-10-physical-hardware-operations.png`, { path: physicalHardwareCompletePath });
                           
                           console.log('✅ 物理硬件菜单操作测试完成');
                         } catch (error) {
                           console.log('❌ 未找到展开后的div元素:', error.message);
                         }
                       } catch (error) {
                         console.log('❌ 未找到展开操作按钮:', error.message);
                       }
                     } catch (error) {
                       console.log('❌ 未找到右键菜单div元素:', error.message);
                     }
                   } catch (error) {
                     console.log('❌ 未找到物理硬件菜单元素:', error.message);
                   }
                 }
               } else {
                 console.log('⚠️ cfg配置页面可能未正确加载');
               }
               
               console.log('✅ cfg管理功能测试完成');
            } else {
              console.log('⚠️ 未找到cfg管理按钮，跳过cfg管理测试');
            }
          } else {
            console.log('⚠️ 未找到确认按钮，跳过提交操作');
          }
        } else {
          console.log('⚠️ 未找到站名输入框，跳过输入操作');
        }
      } else {
        console.log('⚠️ 未找到新建站按钮，跳过站新建功能测试');
        
        // 截图6: 当前页面状态（未找到新建按钮时）
        const noCreateButtonPath = testInfo.outputPath(`${testCaseName}-06-no-create-button.png`);
        await window.screenshot({ path: noCreateButtonPath, fullPage: true });
        console.log(`📸 已保存未找到新建按钮时的页面截图: ${testCaseName}-06-no-create-button.png`);
        await testInfo.attach(`${testCaseName}-06-no-create-button.png`, { path: noCreateButtonPath });
      }
      
      console.log('🎉 Electron应用站管理器基础测试完成！');
      
      // 检查是否启用高级测试功能
      if (TEST_CONFIG.enableAdvancedTests) {
        
        // 步骤11: 测试应用关闭流程和Modal对话框处理
        if (TEST_CONFIG.enableCloseFlowTest) {
          console.log('🔄 开始测试应用关闭流程...');
        } else {
          console.log('⏭️ 应用关闭流程测试已禁用，跳过此步骤');
        }
        
        // 只有启用关闭流程测试时才执行
        if (TEST_CONFIG.enableCloseFlowTest) {
          // 清除本地存储数据，确保触发关闭确认对话框
          await window.evaluate(() => {
                localStorage.clear();
                sessionStorage.clear();
          });
          
          // 先设置对话框处理逻辑，然后触发关闭
          console.log('🔧 设置关闭确认对话框处理逻辑...');
          
          // 在触发关闭之前，先等待对话框出现并处理
            const handleDialogAndClose = async () => {
              // 预先准备所有需要的locator
              const dialogSelector = 'text=是否退出程序?';
              const exitButtonSelector = 'button:has-text("正常退出(关闭服务)")';
              
              console.log('🚪 模拟用户关闭操作...');
              
              // 同时启动对话框监听和关闭触发
              const [dialogResult] = await Promise.all([
                // 对话框处理Promise
                (async () => {
                  try {
                    // 等待对话框出现
                    console.log('⏳ 等待关闭确认对话框出现...');
                    await window.waitForSelector(dialogSelector, { timeout: 15000 });
                    console.log('✅ 关闭确认对话框已出现');
                    
                    // 截图6: 关闭确认对话框
                    const closeDialogPath = testInfo.outputPath(`${testCaseName}-06-close-dialog.png`);
                    await window.screenshot({ path: closeDialogPath, fullPage: true });
                    console.log(`📸 已保存关闭对话框截图: ${testCaseName}-06-close-dialog.png`);
                    await testInfo.attach(`${testCaseName}-06-close-dialog.png`, { path: closeDialogPath });
                    
                    // 验证对话框内容
                    try {
                      const dialogElement = await window.locator(dialogSelector).first();
                      const dialogText = await dialogElement.textContent();
                      console.log(`📋 对话框内容: ${dialogText}`);
                    } catch (textError) {
                      console.log('ℹ️ 无法获取对话框文本，但对话框已确认存在');
                    }
                    
                    // 查找并点击"正常退出(关闭服务)"按钮
                    const exitButton = window.locator(exitButtonSelector);
                    await exitButton.waitFor({ timeout: 5000 });
                    await exitButton.click({ force: true });
                    console.log('✅ 已点击正常退出(关闭服务)按钮');
                    
                    return 'success';
                  } catch (error) {
                    console.error('❌ 对话框处理失败:', error.message);
                    throw error;
                  }
                })(),
                
                // 延迟触发关闭事件，给对话框监听器时间准备
                (async () => {
                  await new Promise(resolve => setTimeout(resolve, 1000));
                  await window.evaluate(() => {
                    window.close();
                  });
                })()
              ]);
            };
          
          // 执行对话框处理
          try {
            await handleDialogAndClose();
            
            // 等待应用实际关闭
            await electronApp.close();
            console.log('✅ 应用已正常关闭');
            
          } catch (modalError) {
            console.error('❌ 处理关闭确认对话框时出错:', modalError.message);
            throw modalError;
          }
        } else {
          // 如果禁用关闭流程测试，直接关闭应用
          try {
            await electronApp.close();
            console.log('✅ 应用已直接关闭（跳过关闭流程测试）');
          } catch (closeError) {
            console.warn('⚠️ 应用关闭时出现问题:', closeError.message);
          }
        }
        
        // 记录测试结束时间并收集日志
        const testEndTime = new Date();
        console.log(`⏰ 测试结束时间: ${testEndTime.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })} (本地时间)`);
        console.log(`⏰ 测试结束时间(UTC): ${testEndTime.toISOString()}`);
        console.log(`⏱️ 测试总耗时: ${testEndTime.getTime() - testStartTime.getTime()}ms`);
        
        // 收集测试期间的后台服务日志（可选）
        if (TEST_CONFIG.enableLogCollection) {
          try {
            console.log('📋 开始收集测试期间的后台服务日志...');
            const logOutputDir = testInfo.outputPath('service-logs');
            
            const logCollector = new LogCollector();
            const logResults = await logCollector.collectLogs(testStartTime, testEndTime, logOutputDir);
            
            // 将收集的日志文件附加到测试报告
            for (const [serviceName, result] of Object.entries(logResults)) {
              if (result.success && result.count > 0) {
                await testInfo.attach(`${serviceName}日志`, {
                  path: result.file,
                  contentType: 'text/plain'
                });
                console.log(`📎 已附加${serviceName}日志到测试报告: ${result.count}条记录`);
              } else {
                console.log(`ℹ️ ${serviceName}在测试期间无相关日志记录`);
              }
            }
            
            console.log('✅ 后台服务日志收集完成');
          } catch (logError) {
            console.warn('⚠️ 日志收集失败，但不影响测试结果:', logError.message);
          }
        } else {
          console.log('⏭️ 日志收集功能已禁用，跳过日志收集步骤');
        }
      }
      
      
      
    } catch (error) {
      console.error('❌ Electron应用站管理器测试失败:', error.message);
      console.error('📋 错误堆栈:', error.stack);
      
      // 即使测试失败，也尝试收集日志用于调试（如果启用）
      if (TEST_CONFIG.enableLogCollection) {
        try {
          const testEndTime = new Date();
          console.log('📋 测试失败，尝试收集调试日志...');
          const logOutputDir = testInfo.outputPath('debug-logs');
          
          const logCollector = new LogCollector();
          const logResults = await logCollector.collectLogs(testStartTime, testEndTime, logOutputDir);
          
          for (const [serviceName, result] of Object.entries(logResults)) {
            if (result.success) {
              await testInfo.attach(`调试-${serviceName}日志`, {
                path: result.file,
                contentType: 'text/plain'
              });
            }
          }
          console.log('✅ 调试日志收集完成');
        } catch (debugLogError) {
          console.warn('⚠️ 调试日志收集失败:', debugLogError.message);
        }
      } else {
        console.log('⏭️ 调试日志收集功能已禁用');
      }
      
      throw error;
    }
  });
  

});